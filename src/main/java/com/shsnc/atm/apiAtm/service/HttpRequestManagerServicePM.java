package com.shsnc.atm.apiAtm.service;

import com.shsnc.atm.apiAtm.model.HttpRequestDocumentPM;
import com.shsnc.atm.apiAtm.model.HttpRequestPM;
import com.shsnc.atm.apiAtm.model.HttpResponsePM;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.HashMap;

/**
 * HTTP请求管理服务
 */
@Service
public class HttpRequestManagerServicePM {

    private static final Logger logger = LoggerFactory.getLogger(HttpRequestManagerServicePM.class);

    @Autowired
    private ElasticsearchServicePM elasticsearchService;

    private static final String HTTP_REQUESTS_INDEX = "http_requests_pm";

    /**
     * 保存HTTP请求
     */
    public HttpRequestDocumentPM saveRequest(String name, String description, HttpRequestPM request, String category) {
        try {
            logger.info("保存HTTP请求: {}", name);

            HttpRequestDocumentPM requestDoc = HttpRequestDocumentPM.fromHttpRequest(request, name, description);
            requestDoc.setCategory(category);

            String savedId = elasticsearchService.saveDocument(HTTP_REQUESTS_INDEX, requestDoc.getId(), requestDoc);

            logger.info("HTTP请求保存成功，ID: {}", savedId);
            return requestDoc;
        } catch (Exception e) {
            logger.error("保存HTTP请求失败: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to save HTTP request: " + e.getMessage(), e);
        }
    }

    /**
     * 获取HTTP请求
     */
    public HttpRequestDocumentPM getRequest(String requestId) {
        try {
            HttpRequestDocumentPM request = elasticsearchService.getDocument(HTTP_REQUESTS_INDEX, requestId, HttpRequestDocumentPM.class);
            
            if (request != null && Boolean.TRUE.equals(request.getIsActive())) {
                return request;
            }
            return null;
        } catch (Exception e) {
            logger.error("获取HTTP请求失败: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to get HTTP request: " + e.getMessage(), e);
        }
    }

    /**
     * 更新HTTP请求
     */
    public HttpRequestDocumentPM updateRequest(String requestId, String name, String description, 
                                              HttpRequestPM request, String category, String[] tags) {
        try {
            logger.info("更新HTTP请求: {}", requestId);

            HttpRequestDocumentPM existingRequest = elasticsearchService.getDocument(HTTP_REQUESTS_INDEX, requestId, HttpRequestDocumentPM.class);
            
            if (existingRequest == null) {
                throw new RuntimeException("HTTP request not found: " + requestId);
            }

            // 更新请求信息
            existingRequest.setName(name);
            existingRequest.setDescription(description);
            existingRequest.setMethod(request.getMethod());
            existingRequest.setUrl(request.getUrl());
            existingRequest.setHeaders(request.getHeaders());
            existingRequest.setBody(request.getBody());
            existingRequest.setPreRequestScript(request.getPreRequestScript());
            existingRequest.setTestScript(request.getTestScript());
            existingRequest.setPreRequestScriptIds(request.getPreRequestScriptIds());
            existingRequest.setTestScriptIds(request.getTestScriptIds());
            existingRequest.setPreRequestActions(request.getPreRequestActions());
            existingRequest.setPostActions(request.getPostActions());
            existingRequest.setEnvironmentId(request.getEnvironmentId());
            existingRequest.setCategory(category);
            existingRequest.setTags(tags);
            existingRequest.incrementVersion();

            // 使用标准的ES更新操作
            logger.info("更新HTTP请求到ES: {}", requestId);
            elasticsearchService.updateDocument(HTTP_REQUESTS_INDEX, requestId, existingRequest);
            logger.info("成功更新HTTP请求: {}", requestId);

            logger.info("HTTP请求更新成功: {}", requestId);
            return existingRequest;
        } catch (Exception e) {
            logger.error("更新HTTP请求失败: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to update HTTP request: " + e.getMessage(), e);
        }
    }

    /**
     * 删除HTTP请求
     */
    public void deleteRequest(String requestId) {
        try {
            logger.info("删除HTTP请求: {}", requestId);

            HttpRequestDocumentPM existingRequest = elasticsearchService.getDocument(HTTP_REQUESTS_INDEX, requestId, HttpRequestDocumentPM.class);
            
            if (existingRequest == null) {
                throw new RuntimeException("HTTP request not found: " + requestId);
            }

            // 软删除：设置为非活动状态
            existingRequest.setIsActive(false);

            // 使用标准的ES更新操作
            logger.info("软删除HTTP请求: {}", requestId);
            elasticsearchService.updateDocument(HTTP_REQUESTS_INDEX, requestId, existingRequest);
            logger.info("成功软删除HTTP请求: {}", requestId);

            logger.info("HTTP请求删除成功: {}", requestId);
        } catch (Exception e) {
            logger.error("删除HTTP请求失败: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to delete HTTP request: " + e.getMessage(), e);
        }
    }

    /**
     * 获取所有HTTP请求列表
     */
    public List<HttpRequestDocumentPM> getAllRequests() {
        try {
            logger.info("获取所有HTTP请求列表");
            List<HttpRequestDocumentPM> allRequests = elasticsearchService.getAllDocuments(HTTP_REQUESTS_INDEX, HttpRequestDocumentPM.class);
            
            // 过滤出活动的请求
            return allRequests.stream()
                    .filter(request -> Boolean.TRUE.equals(request.getIsActive()))
                    .toList();
        } catch (Exception e) {
            logger.error("获取HTTP请求列表失败: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to get HTTP requests list: " + e.getMessage(), e);
        }
    }

    /**
     * 按分类搜索HTTP请求
     */
    public List<HttpRequestDocumentPM> getRequestsByCategory(String category) {
        try {
            logger.info("按分类搜索HTTP请求: {}", category);
            return elasticsearchService.searchDocuments(HTTP_REQUESTS_INDEX, "category", category, HttpRequestDocumentPM.class)
                    .stream()
                    .filter(request -> Boolean.TRUE.equals(request.getIsActive()))
                    .toList();
        } catch (Exception e) {
            logger.error("按分类搜索HTTP请求失败: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to search HTTP requests by category: " + e.getMessage(), e);
        }
    }

    /**
     * 按名称搜索HTTP请求
     */
    public List<HttpRequestDocumentPM> searchRequestsByName(String searchQuery) {
        try {
            logger.info("按名称搜索HTTP请求: {}", searchQuery);
            return elasticsearchService.searchDocuments(HTTP_REQUESTS_INDEX, "name", searchQuery, HttpRequestDocumentPM.class)
                    .stream()
                    .filter(request -> Boolean.TRUE.equals(request.getIsActive()))
                    .toList();
        } catch (Exception e) {
            logger.error("按名称搜索HTTP请求失败: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to search HTTP requests by name: " + e.getMessage(), e);
        }
    }

    /**
     * 更新请求执行信息
     */
    public void updateRequestExecutionInfo(String requestId, HttpResponsePM response) {
        try {
            if (requestId == null || requestId.isEmpty()) {
                return; // 如果没有请求ID，跳过更新
            }

            HttpRequestDocumentPM requestDoc = elasticsearchService.getDocument(HTTP_REQUESTS_INDEX, requestId, HttpRequestDocumentPM.class);
            
            if (requestDoc != null) {
                requestDoc.updateExecutionInfo(
                    response.getStatusCode(),
                    response.getResponseTime()
                );

                // 使用标准的ES更新操作
                logger.debug("更新请求执行信息到ES: {}", requestId);
                elasticsearchService.updateDocument(HTTP_REQUESTS_INDEX, requestId, requestDoc);
                logger.info("更新请求执行信息成功: {}", requestId);
            }
        } catch (Exception e) {
            logger.warn("更新请求执行信息失败: {}", e.getMessage());
            // 不抛出异常，避免影响主要的请求执行流程
        }
    }

    /**
     * 复制HTTP请求
     */
    public HttpRequestDocumentPM duplicateRequest(String requestId, String newName) {
        try {
            logger.info("复制HTTP请求: {} -> {}", requestId, newName);

            HttpRequestDocumentPM originalRequest = getRequest(requestId);
            if (originalRequest == null) {
                throw new RuntimeException("Original request not found: " + requestId);
            }

            // 创建新的请求文档
            HttpRequestDocumentPM newRequest = HttpRequestDocumentPM.fromHttpRequest(
                originalRequest.toHttpRequest(), 
                newName, 
                originalRequest.getDescription() + " (副本)"
            );
            newRequest.setCategory(originalRequest.getCategory());
            newRequest.setTags(originalRequest.getTags());

            String savedId = elasticsearchService.saveDocument(HTTP_REQUESTS_INDEX, newRequest.getId(), newRequest);

            logger.info("HTTP请求复制成功，新ID: {}", savedId);
            return newRequest;
        } catch (Exception e) {
            logger.error("复制HTTP请求失败: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to duplicate HTTP request: " + e.getMessage(), e);
        }
    }

    /**
     * 获取HTTP请求统计信息
     */
    public Map<String, Object> getRequestsStats() {
        try {
            logger.info("获取HTTP请求统计信息");
            
            List<HttpRequestDocumentPM> allRequests = getAllRequests();
            
            Map<String, Object> stats = new HashMap<>();
            stats.put("totalCount", allRequests.size());
            stats.put("activeCount", allRequests.stream()
                    .filter(request -> Boolean.TRUE.equals(request.getIsActive()))
                    .count());
            
            // 按分类统计
            Map<String, Long> categoryStats = new HashMap<>();
            allRequests.forEach(request -> {
                String category = request.getCategory() != null ? request.getCategory() : "未分类";
                categoryStats.put(category, categoryStats.getOrDefault(category, 0L) + 1);
            });
            stats.put("categoryStats", categoryStats);
            
            // 按方法统计
            Map<String, Long> methodStats = new HashMap<>();
            allRequests.forEach(request -> {
                String method = request.getMethod() != null ? request.getMethod() : "UNKNOWN";
                methodStats.put(method, methodStats.getOrDefault(method, 0L) + 1);
            });
            stats.put("methodStats", methodStats);
            
            return stats;
        } catch (Exception e) {
            logger.error("获取HTTP请求统计信息失败: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to get HTTP requests stats: " + e.getMessage(), e);
        }
    }
}
