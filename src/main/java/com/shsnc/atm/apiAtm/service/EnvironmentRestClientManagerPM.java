package com.shsnc.atm.apiAtm.service;

import com.shsnc.atm.apiAtm.model.EnvironmentPM;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 环境管理器 - 使用RestHighLevelClient
 */
@Service
public class EnvironmentRestClientManagerPM {

    private static final Logger logger = LoggerFactory.getLogger(EnvironmentRestClientManagerPM.class);
    private static final String ENVIRONMENT_INDEX = "pm_environments";

    @Autowired
    private ElasticsearchServicePM elasticsearchService;

    /**
     * 环境ES实体类
     */
    public static class EnvironmentDocument {
        private String id;
        private String name;
        private Map<String, String> variables;
        private String baseUrl;
        private String createdAt;
        private String updatedAt;
        private String description;
        private Boolean isActive;

        public EnvironmentDocument() {
            this.variables = new HashMap<>();
            this.baseUrl = "http://192.168.450.143:8080"; // 默认前置URL
            this.createdAt = LocalDateTime.now().toString();
            this.updatedAt = LocalDateTime.now().toString();
            this.isActive = true;
        }

        public EnvironmentDocument(String id, String name) {
            this();
            this.id = id;
            this.name = name;
        }

        // Getters and Setters
        public String getId() { return id; }
        public void setId(String id) { this.id = id; }
        
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
        
        public Map<String, String> getVariables() { return variables; }
        public void setVariables(Map<String, String> variables) { this.variables = variables; }
        
        public String getCreatedAt() { return createdAt; }
        public void setCreatedAt(String createdAt) { this.createdAt = createdAt; }
        
        public String getUpdatedAt() { return updatedAt; }
        public void setUpdatedAt(String updatedAt) { this.updatedAt = updatedAt; }
        
        public String getDescription() { return description; }
        public void setDescription(String description) { this.description = description; }
        
        public Boolean getIsActive() { return isActive; }
        public void setIsActive(Boolean isActive) { this.isActive = isActive; }

        public String getBaseUrl() { return baseUrl; }
        public void setBaseUrl(String baseUrl) { this.baseUrl = baseUrl; }

        public void setVariable(String key, String value) {
            if (this.variables == null) {
                this.variables = new HashMap<>();
            }
            this.variables.put(key, value);
            this.updatedAt = LocalDateTime.now().toString();
        }

        public String getVariable(String key) {
            return this.variables != null ? this.variables.get(key) : null;
        }

        public void removeVariable(String key) {
            if (this.variables != null) {
                this.variables.remove(key);
                this.updatedAt = LocalDateTime.now().toString();
            }
        }

        public boolean hasVariable(String key) {
            return this.variables != null && this.variables.containsKey(key);
        }
    }

    /**
     * 创建环境
     */
    public EnvironmentPM createEnvironment(String name) {
        logger.info("开始创建环境，名称: {}", name);

        if (name == null || name.trim().isEmpty()) {
            throw new IllegalArgumentException("Environment name cannot be empty");
        }

        // 检查名称是否已存在
        logger.info("检查环境名称是否已存在: {}", name.trim());
        if (existsByName(name.trim())) {
            throw new IllegalArgumentException("Environment with name '" + name + "' already exists");
        }

        String envId = UUID.randomUUID().toString();
        logger.info("生成环境ID: {}", envId);

        EnvironmentDocument envDoc = new EnvironmentDocument(envId, name.trim());
        logger.info("创建环境文档对象，ID: {}, 名称: {}", envDoc.getId(), envDoc.getName());

        try {
            logger.info("开始保存环境文档到ES，索引: {}, ID: {}", ENVIRONMENT_INDEX, envId);
            elasticsearchService.saveDocument(ENVIRONMENT_INDEX, envId, envDoc);
            logger.info("成功保存环境文档到ES");

            EnvironmentPM result = convertToEnvironmentPM(envDoc);
            logger.info("成功创建环境，返回结果: {}", result.getName());
            return result;
        } catch (Exception e) {
            logger.error("创建环境失败", e);
            throw new RuntimeException("Failed to create environment: " + e.getMessage(), e);
        }
    }

    /**
     * 获取环境
     */
    public EnvironmentPM getEnvironment(String environmentId) {
        if (environmentId == null || environmentId.trim().isEmpty()) {
            return null;
        }

        try {
            EnvironmentDocument envDoc = elasticsearchService.getDocument(ENVIRONMENT_INDEX, environmentId, EnvironmentDocument.class);
            return envDoc != null && Boolean.TRUE.equals(envDoc.getIsActive()) ? convertToEnvironmentPM(envDoc) : null;
        } catch (Exception e) {
            throw new RuntimeException("Failed to get environment: " + e.getMessage(), e);
        }
    }

    /**
     * 获取所有环境
     */
    public List<EnvironmentPM> getAllEnvironments() {
        try {
            logger.info("开始从ES获取所有环境，索引: {}", ENVIRONMENT_INDEX);
            List<EnvironmentDocument> envDocs = elasticsearchService.getAllDocuments(ENVIRONMENT_INDEX, EnvironmentDocument.class);
            logger.info("从ES获取到 {} 个环境文档", envDocs.size());

            List<EnvironmentPM> activeEnvs = envDocs.stream()
                    .filter(doc -> Boolean.TRUE.equals(doc.getIsActive()))
                    .map(this::convertToEnvironmentPM)
                    .collect(Collectors.toList());

            logger.info("过滤后的活动环境数量: {}", activeEnvs.size());
            return activeEnvs;
        } catch (Exception e) {
            logger.error("获取所有环境失败", e);
            throw new RuntimeException("Failed to get all environments: " + e.getMessage(), e);
        }
    }

    /**
     * 更新环境
     */
    public EnvironmentPM updateEnvironment(String environmentId, String name, String description) {
        EnvironmentDocument envDoc = elasticsearchService.getDocument(ENVIRONMENT_INDEX, environmentId, EnvironmentDocument.class);
        if (envDoc == null || !Boolean.TRUE.equals(envDoc.getIsActive())) {
            throw new IllegalArgumentException("Environment not found with ID: " + environmentId);
        }

        if (name != null && !name.trim().isEmpty()) {
            envDoc.setName(name.trim());
        }
        if (description != null) {
            envDoc.setDescription(description);
        }
        envDoc.setUpdatedAt(LocalDateTime.now().toString());

        try {
            // 使用删除+重建方式避免ES更新解析问题
            logger.info("使用删除+重建方式更新环境: {}", environmentId);
            try {
                elasticsearchService.deleteDocument(ENVIRONMENT_INDEX, environmentId);
                logger.info("成功删除旧环境文档: {}", environmentId);
            } catch (Exception deleteException) {
                logger.warn("删除旧环境文档失败（可能不存在）: {}", deleteException.getMessage());
            }

            elasticsearchService.saveDocument(ENVIRONMENT_INDEX, environmentId, envDoc);
            logger.info("成功重新创建环境文档: {}", environmentId);

            return convertToEnvironmentPM(envDoc);
        } catch (Exception e) {
            logger.error("更新环境失败", e);
            throw new RuntimeException("Failed to update environment: " + e.getMessage(), e);
        }
    }

    /**
     * 删除环境
     */
    public boolean deleteEnvironment(String environmentId) {
        try {
            EnvironmentDocument envDoc = elasticsearchService.getDocument(ENVIRONMENT_INDEX, environmentId, EnvironmentDocument.class);
            if (envDoc != null) {
                envDoc.setIsActive(false);
                envDoc.setUpdatedAt(LocalDateTime.now().toString());
                elasticsearchService.updateDocument(ENVIRONMENT_INDEX, environmentId, envDoc);
                return true;
            }
            return false;
        } catch (Exception e) {
            throw new RuntimeException("Failed to delete environment: " + e.getMessage(), e);
        }
    }

    /**
     * 设置环境变量 (使用安全的重新索引方式)
     */
    public void setEnvironmentVariable(String environmentId, String key, String value) {
        logger.info("设置环境变量，环境ID: {}, 变量名: {}, 变量值: {}", environmentId, key, value);

        if (key == null || key.trim().isEmpty()) {
            logger.warn("变量名为空，跳过设置");
            return;
        }

        try {
            logger.info("获取环境文档，ID: {}", environmentId);
            EnvironmentDocument envDoc = elasticsearchService.getDocument(ENVIRONMENT_INDEX, environmentId, EnvironmentDocument.class);

            if (envDoc == null) {
                logger.error("环境文档不存在，ID: {}", environmentId);
                throw new IllegalArgumentException("Environment not found with ID: " + environmentId);
            }

            if (!Boolean.TRUE.equals(envDoc.getIsActive())) {
                logger.error("环境文档未激活，ID: {}, isActive: {}", environmentId, envDoc.getIsActive());
                throw new IllegalArgumentException("Environment is not active with ID: " + environmentId);
            }

            logger.info("设置变量到环境文档");
            envDoc.setVariable(key, value);
            envDoc.setUpdatedAt(LocalDateTime.now().toString());

            // 使用标准的ES更新操作
            logger.info("更新环境文档到ES");
            elasticsearchService.updateDocument(ENVIRONMENT_INDEX, environmentId, envDoc);
            logger.info("成功更新环境变量");

        } catch (Exception e) {
            logger.error("设置环境变量失败", e);
            throw new RuntimeException("Failed to set environment variable: " + e.getMessage(), e);
        }
    }

    /**
     * 获取环境变量
     */
    public String getEnvironmentVariable(String environmentId, String key) {
        if (environmentId == null || key == null) {
            return null;
        }

        try {
            EnvironmentDocument envDoc = elasticsearchService.getDocument(ENVIRONMENT_INDEX, environmentId, EnvironmentDocument.class);
            if (envDoc != null && Boolean.TRUE.equals(envDoc.getIsActive())) {
                // 特殊处理baseUrl变量
                if ("baseUrl".equals(key)) {
                    return envDoc.getBaseUrl();
                }
                return envDoc.getVariable(key);
            }
            return null;
        } catch (Exception e) {
            throw new RuntimeException("Failed to get environment variable: " + e.getMessage(), e);
        }
    }

    /**
     * 删除环境变量 (使用删除+重建方式)
     */
    public void deleteEnvironmentVariable(String environmentId, String key) {
        logger.info("删除环境变量，环境ID: {}, 变量名: {}", environmentId, key);

        if (key == null || key.trim().isEmpty()) {
            logger.warn("变量名为空，跳过删除");
            return;
        }

        try {
            EnvironmentDocument envDoc = elasticsearchService.getDocument(ENVIRONMENT_INDEX, environmentId, EnvironmentDocument.class);
            if (envDoc != null && Boolean.TRUE.equals(envDoc.getIsActive())) {
                envDoc.removeVariable(key);
                envDoc.setUpdatedAt(LocalDateTime.now().toString());

                // 使用删除+重建方式
                logger.info("使用删除+重建方式保存环境文档");

                try {
                    elasticsearchService.deleteDocument(ENVIRONMENT_INDEX, environmentId);
                    logger.info("成功删除旧环境文档");
                } catch (Exception deleteException) {
                    logger.warn("删除旧文档失败（可能不存在）: {}", deleteException.getMessage());
                }

                elasticsearchService.saveDocument(ENVIRONMENT_INDEX, environmentId, envDoc);
                logger.info("成功重新创建环境文档，删除变量完成");
            } else {
                logger.warn("环境文档不存在或未激活，ID: {}", environmentId);
            }
        } catch (Exception e) {
            logger.error("删除环境变量失败", e);
            throw new RuntimeException("Failed to delete environment variable: " + e.getMessage(), e);
        }
    }

    /**
     * 获取环境的所有变量
     */
    public Map<String, String> getEnvironmentVariables(String environmentId) {
        try {
            EnvironmentDocument envDoc = elasticsearchService.getDocument(ENVIRONMENT_INDEX, environmentId, EnvironmentDocument.class);
            if (envDoc != null && Boolean.TRUE.equals(envDoc.getIsActive())) {
                return new HashMap<>(envDoc.getVariables());
            }
            return new HashMap<>();
        } catch (Exception e) {
            throw new RuntimeException("Failed to get environment variables: " + e.getMessage(), e);
        }
    }

    /**
     * 检查环境名称是否存在
     */
    public boolean existsByName(String name) {
        try {
            List<EnvironmentDocument> envDocs = elasticsearchService.searchDocuments(ENVIRONMENT_INDEX, "name", name, EnvironmentDocument.class);
            return envDocs.stream().anyMatch(doc -> Boolean.TRUE.equals(doc.getIsActive()));
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 批量设置环境变量 (使用安全的重新索引方式)
     */
    public void setEnvironmentVariables(String environmentId, Map<String, String> variables) {
        logger.info("批量设置环境变量，环境ID: {}, 变量数量: {}", environmentId, variables != null ? variables.size() : 0);

        if (variables == null || variables.isEmpty()) {
            logger.warn("变量列表为空，跳过设置");
            return;
        }

        try {
            EnvironmentDocument envDoc = elasticsearchService.getDocument(ENVIRONMENT_INDEX, environmentId, EnvironmentDocument.class);
            if (envDoc != null && Boolean.TRUE.equals(envDoc.getIsActive())) {
                for (Map.Entry<String, String> entry : variables.entrySet()) {
                    envDoc.setVariable(entry.getKey(), entry.getValue());
                    logger.debug("设置变量: {} = {}", entry.getKey(), entry.getValue());
                }
                envDoc.setUpdatedAt(LocalDateTime.now().toString());

                // 直接使用删除+重建方式
                logger.info("使用删除+重建方式批量保存环境变量");

                try {
                    elasticsearchService.deleteDocument(ENVIRONMENT_INDEX, environmentId);
                    logger.info("成功删除旧环境文档");
                } catch (Exception deleteException) {
                    logger.warn("删除旧文档失败（可能不存在）: {}", deleteException.getMessage());
                }

                elasticsearchService.saveDocument(ENVIRONMENT_INDEX, environmentId, envDoc);
                logger.info("成功重新创建环境文档，批量设置完成");
            } else {
                throw new IllegalArgumentException("Environment not found with ID: " + environmentId);
            }
        } catch (Exception e) {
            logger.error("批量设置环境变量失败", e);
            throw new RuntimeException("Failed to set environment variables: " + e.getMessage(), e);
        }
    }

    /**
     * 更新环境名称
     */
    public boolean updateEnvironmentName(String environmentId, String name) {
        try {
            EnvironmentDocument envDoc = elasticsearchService.getDocument(ENVIRONMENT_INDEX, environmentId, EnvironmentDocument.class);
            if (envDoc != null && Boolean.TRUE.equals(envDoc.getIsActive())) {
                envDoc.setName(name);
                envDoc.setUpdatedAt(LocalDateTime.now().toString());
                elasticsearchService.updateDocument(ENVIRONMENT_INDEX, environmentId, envDoc);
                return true;
            }
            return false;
        } catch (Exception e) {
            throw new RuntimeException("Failed to update environment name: " + e.getMessage(), e);
        }
    }

    /**
     * 检查环境是否存在
     */
    public boolean environmentExists(String environmentId) {
        try {
            EnvironmentDocument envDoc = elasticsearchService.getDocument(ENVIRONMENT_INDEX, environmentId, EnvironmentDocument.class);
            return envDoc != null && Boolean.TRUE.equals(envDoc.getIsActive());
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 更新环境前置URL
     */
    public boolean updateEnvironmentBaseUrl(String environmentId, String baseUrl) {
        logger.info("开始更新环境前置URL，环境ID: {}, 新URL: {}", environmentId, baseUrl);

        if (environmentId == null || baseUrl == null) {
            logger.warn("环境ID或baseUrl为空，environmentId: {}, baseUrl: {}", environmentId, baseUrl);
            return false;
        }

        try {
            logger.info("从ES获取环境文档，索引: {}, ID: {}", ENVIRONMENT_INDEX, environmentId);
            EnvironmentDocument envDoc = elasticsearchService.getDocument(ENVIRONMENT_INDEX, environmentId, EnvironmentDocument.class);

            if (envDoc == null) {
                logger.warn("环境文档不存在，environmentId: {}", environmentId);
                return false;
            }

            if (!Boolean.TRUE.equals(envDoc.getIsActive())) {
                logger.warn("环境文档未激活，environmentId: {}, isActive: {}", environmentId, envDoc.getIsActive());
                return false;
            }

            logger.info("更新环境文档，原baseUrl: {}, 新baseUrl: {}", envDoc.getBaseUrl(), baseUrl);

            // 确保baseUrl字段存在（兼容旧文档）
            if (envDoc.getBaseUrl() == null) {
                logger.info("环境文档没有baseUrl字段，这是一个旧文档，正在添加baseUrl字段");
            }

            envDoc.setBaseUrl(baseUrl);
            envDoc.setUpdatedAt(LocalDateTime.now().toString());

            logger.info("保存更新后的环境文档到ES");

            // 使用更安全的更新方式
            try {
                elasticsearchService.updateDocument(ENVIRONMENT_INDEX, environmentId, envDoc);
                logger.info("成功更新环境前置URL");
                return true;
            } catch (Exception updateException) {
                logger.error("ES更新失败，尝试重新索引文档", updateException);

                // 如果更新失败，尝试删除后重新创建
                try {
                    elasticsearchService.deleteDocument(ENVIRONMENT_INDEX, environmentId);
                    elasticsearchService.saveDocument(ENVIRONMENT_INDEX, environmentId, envDoc);
                    logger.info("通过重新索引成功更新环境前置URL");
                    return true;
                } catch (Exception reindexException) {
                    logger.error("重新索引也失败", reindexException);
                    throw reindexException;
                }
            }

        } catch (Exception e) {
            logger.error("更新环境前置URL失败", e);
            throw new RuntimeException("Failed to update environment base URL: " + e.getMessage(), e);
        }
    }

    /**
     * 清除所有环境数据
     */
    public boolean clearAllEnvironments() {
        try {
            logger.info("开始清除所有环境数据，索引: {}", ENVIRONMENT_INDEX);

            // 删除整个索引
            boolean indexExists = elasticsearchService.indexExists(ENVIRONMENT_INDEX);
            if (indexExists) {
                elasticsearchService.deleteIndex(ENVIRONMENT_INDEX);
                logger.info("成功删除环境索引: {}", ENVIRONMENT_INDEX);
            } else {
                logger.info("环境索引不存在: {}", ENVIRONMENT_INDEX);
            }

            return true;
        } catch (Exception e) {
            logger.error("清除环境数据失败", e);
            return false;
        }
    }

    /**
     * 重建环境索引
     */
    public boolean rebuildEnvironmentIndex() {
        try {
            logger.info("开始重建环境索引: {}", ENVIRONMENT_INDEX);

            // 删除旧索引（如果存在）
            boolean indexExists = elasticsearchService.indexExists(ENVIRONMENT_INDEX);
            if (indexExists) {
                elasticsearchService.deleteIndex(ENVIRONMENT_INDEX);
                logger.info("删除旧的环境索引: {}", ENVIRONMENT_INDEX);
            }

            // 创建新索引
            elasticsearchService.createIndex(ENVIRONMENT_INDEX);
            logger.info("创建新的环境索引: {}", ENVIRONMENT_INDEX);

            return true;
        } catch (Exception e) {
            logger.error("重建环境索引失败", e);
            return false;
        }
    }

    /**
     * 转换为EnvironmentPM
     */
    private EnvironmentPM convertToEnvironmentPM(EnvironmentDocument envDoc) {
        EnvironmentPM env = new EnvironmentPM(envDoc.getId(), envDoc.getName());
        env.setVariables(new HashMap<>(envDoc.getVariables()));
        env.setDescription(envDoc.getDescription());
        env.setBaseUrl(envDoc.getBaseUrl());
        return env;
    }
}
