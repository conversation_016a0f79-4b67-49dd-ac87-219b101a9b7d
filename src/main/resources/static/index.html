<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Postman-like API Client</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: #ff6c37;
            color: white;
            padding: 20px;
            text-align: center;
        }
        .tabs {
            display: flex;
            background: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
        }
        .tab {
            padding: 15px 25px;
            cursor: pointer;
            border-bottom: 3px solid transparent;
            transition: all 0.3s;
        }
        .tab.active {
            background: white;
            border-bottom-color: #ff6c37;
        }
        .tab-content {
            padding: 20px;
            display: none;
        }
        .tab-content.active {
            display: block;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select, textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        textarea {
            height: 100px;
            resize: vertical;
        }
        .btn {
            background: #ff6c37;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        .btn:hover {
            background: #e55a2b;
        }
        .btn-secondary {
            background: #6c757d;
        }
        .btn-secondary:hover {
            background: #5a6268;
        }
        .response {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 4px;
            border: 1px solid #dee2e6;
        }
        .env-list {
            list-style: none;
            padding: 0;
        }
        .env-item {
            padding: 10px;
            border: 1px solid #ddd;
            margin-bottom: 10px;
            border-radius: 4px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .env-item.active {
            background: #e7f3ff;
            border-color: #007bff;
        }
        .variable-list {
            margin-top: 10px;
        }
        .variable-item {
            display: flex;
            gap: 10px;
            margin-bottom: 10px;
            align-items: center;
        }
        .variable-item input {
            flex: 1;
        }
        .btn-small {
            padding: 5px 10px;
            font-size: 12px;
        }
        .selected-scripts {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin-bottom: 10px;
        }
        .script-list {
            margin-top: 10px;
        }
        .script-item {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 8px;
            margin-bottom: 5px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .script-item .script-name {
            font-weight: bold;
            color: #495057;
        }
        .script-item .script-description {
            font-size: 12px;
            color: #6c757d;
            margin-top: 2px;
        }
        .script-selector {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            display: none;
            z-index: 1000;
        }
        .script-selector-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            border-radius: 8px;
            padding: 20px;
            width: 80%;
            max-width: 800px;
            max-height: 80%;
            overflow-y: auto;
        }
        .script-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        .script-card {
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            cursor: pointer;
            transition: all 0.3s;
        }
        .script-card:hover {
            border-color: #007bff;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .script-card.selected {
            border-color: #007bff;
            background: #e7f3ff;
        }
        .script-card .script-title {
            font-weight: bold;
            margin-bottom: 5px;
        }
        .script-card .script-meta {
            font-size: 12px;
            color: #6c757d;
            margin-bottom: 10px;
        }
        .script-card .script-preview {
            font-family: monospace;
            font-size: 11px;
            background: #f8f9fa;
            padding: 8px;
            border-radius: 3px;
            max-height: 60px;
            overflow: hidden;
        }
        .action-type-tabs {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
        }
        .action-item {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin-bottom: 8px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .action-item .action-info {
            flex: 1;
        }
        .action-item .action-name {
            font-weight: bold;
            color: #495057;
            margin-bottom: 2px;
        }
        .action-item .action-description {
            font-size: 12px;
            color: #6c757d;
        }
        .action-item .action-controls {
            display: flex;
            gap: 5px;
        }
        .action-item .action-controls button {
            min-width: 30px;
        }
        .wait-config {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
        }
        .params-container {
            border: 1px solid #ddd;
            border-radius: 4px;
            background: #f9f9f9;
            padding: 10px;
            margin-top: 5px;
        }
        .param-header {
            background: #e9ecef;
            padding: 8px;
            border-radius: 4px;
            margin-bottom: 10px;
            font-size: 14px;
        }
        .param-row {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
            padding: 5px;
            background: white;
            border-radius: 4px;
            border: 1px solid #e0e0e0;
        }
        .param-row input {
            margin: 0 5px;
            padding: 5px 8px;
            border: 1px solid #ccc;
            border-radius: 3px;
            font-size: 14px;
        }
        .param-row input:first-child {
            width: 30%;
        }
        .param-row input:nth-child(2) {
            width: 50%;
        }
        .param-row .param-actions {
            width: 20%;
            text-align: center;
        }
        .param-row .btn-remove {
            background: #dc3545;
            color: white;
            border: none;
            padding: 4px 8px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 12px;
        }
        .param-row .btn-remove:hover {
            background: #c82333;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Postman-like API Client</h1>
            <p>基于Nashorn的HTTP请求测试工具</p>
        </div>
        
        <div class="tabs">
            <div class="tab active" onclick="showTab('request')">HTTP请求</div>
            <div class="tab" onclick="showTab('requests')">请求历史</div>
            <div class="tab" onclick="showTab('environment')">环境管理</div>
            <div class="tab" onclick="showTab('globals')">全局变量</div>
            <div class="tab" onclick="showTab('scripts')">脚本管理</div>
        </div>
        
        <!-- HTTP请求标签页 -->
        <div id="request" class="tab-content active">
            <div class="form-group">
                <label>当前环境:</label>
                <div class="current-env-display" style="padding: 8px; background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 4px; font-weight: bold;">
                    未选择环境
                </div>
            </div>

            <div class="form-group">
                <label>请求方法:</label>
                <select id="method">
                    <option value="GET">GET</option>
                    <option value="POST">POST</option>
                    <option value="PUT">PUT</option>
                    <option value="DELETE">DELETE</option>
                    <option value="PATCH">PATCH</option>
                    <option value="HEAD">HEAD</option>
                    <option value="OPTIONS">OPTIONS</option>
                </select>
            </div>
            
            <div class="form-group">
                <label>URL (支持{{变量}}格式):</label>
                <input type="text" id="url" placeholder="例如: {{baseUrl}}/api/users/{userId}" value="{{baseUrl}}/test">
            </div>

            <div class="form-group">
                <label>Path 参数 (路径参数，用于替换URL中的{参数名}):</label>
                <div style="margin-bottom: 10px;">
                    <button class="btn btn-secondary btn-small" onclick="addPathParam()">添加Path参数</button>
                    <button class="btn btn-secondary btn-small" onclick="clearPathParams()">清空</button>
                </div>
                <div id="pathParamsContainer" class="params-container">
                    <div class="param-header">
                        <span style="width: 30%; display: inline-block; font-weight: bold;">参数名</span>
                        <span style="width: 50%; display: inline-block; font-weight: bold;">参数值 (支持{{变量}})</span>
                        <span style="width: 20%; display: inline-block; font-weight: bold;">操作</span>
                    </div>
                    <div id="pathParamsList"></div>
                </div>
            </div>

            <div class="form-group">
                <label>Query 参数 (查询参数，拼接在URL后面):</label>
                <div style="margin-bottom: 10px;">
                    <button class="btn btn-secondary btn-small" onclick="addQueryParam()">添加Query参数</button>
                    <button class="btn btn-secondary btn-small" onclick="clearQueryParams()">清空</button>
                </div>
                <div id="queryParamsContainer" class="params-container">
                    <div class="param-header">
                        <span style="width: 30%; display: inline-block; font-weight: bold;">参数名</span>
                        <span style="width: 50%; display: inline-block; font-weight: bold;">参数值 (支持{{变量}})</span>
                        <span style="width: 20%; display: inline-block; font-weight: bold;">操作</span>
                    </div>
                    <div id="queryParamsList"></div>
                </div>
            </div>

            <div class="form-group">
                <label>请求头 (JSON格式):</label>
                <textarea id="headers" placeholder='{"Content-Type": "application/json", "Authorization": "Bearer {{token}}"}'>{
  "Content-Type": "application/json"
}</textarea>
            </div>
            
            <div class="form-group">
                <label>请求体 (支持{{变量}}格式):</label>
                <textarea id="body" placeholder='{"name": "{{userName}}", "email": "<EMAIL>"}'></textarea>
            </div>
            
            <div class="form-group">
                <label>前置操作:</label>
                <div style="margin-bottom: 10px;">
                    <button class="btn btn-secondary" onclick="showActionSelector('preRequest')">添加操作</button>
                    <button class="btn btn-secondary" onclick="toggleScriptEditor('preScript')">编辑脚本</button>
                </div>
                <div id="selectedPreActions" class="selected-scripts">
                    <p>已配置的前置操作: <span id="preActionCount">0</span> 个</p>
                    <div id="preActionList" class="script-list"></div>
                </div>
                <textarea id="preScript" style="display: none;" placeholder="pm.environment.set('timestamp', Date.now().toString());">// 设置时间戳变量
pm.environment.set('timestamp', Date.now().toString());
console.log('Pre-request script executed');</textarea>
            </div>

            <div class="form-group">
                <label>后置操作:</label>
                <div style="margin-bottom: 10px;">
                    <button class="btn btn-secondary" onclick="showActionSelector('test')">添加操作</button>
                    <button class="btn btn-secondary" onclick="toggleScriptEditor('testScript')">编辑脚本</button>
                </div>
                <div id="selectedPostActions" class="selected-scripts">
                    <p>已配置的后置操作: <span id="postActionCount">0</span> 个</p>
                    <div id="postActionList" class="script-list"></div>
                </div>
                <textarea id="testScript" style="display: none;" placeholder="pm.test('Status code is 200', function () { pm.response.code() === 200; });">// 测试响应状态码
pm.test('Status code is 200', function () {
    pm.expect(pm.response.code()).to.equal(200);
});

// 测试响应时间
pm.test('Response time is less than 1000ms', function () {
    pm.expect(pm.response.responseTime()).to.be.below(1000);
});</textarea>
            </div>
            
            <div class="form-group" style="display: flex; gap: 10px; margin: 15px 0;">
                <button class="btn" onclick="sendRequest()">发送请求</button>
                <button class="btn btn-secondary" onclick="clearResponse()">清空响应</button>
                <button class="btn" style="background: #28a745; border-color: #28a745;" onclick="saveCurrentHttpRequest()">保存请求</button>
                <button class="btn btn-secondary" onclick="showSavedHttpRequests()">管理保存的请求</button>
            </div>
            
            <div id="response" class="response" style="display: none;">
                <h3>响应结果:</h3>
                <pre id="responseContent"></pre>
            </div>
        </div>
        
        <!-- 环境管理标签页 -->
        <div id="environment" class="tab-content">
            <div class="form-group">
                <label>创建新环境:</label>
                <div style="display: flex; gap: 10px;">
                    <input type="text" id="newEnvName" placeholder="环境名称">
                    <button class="btn" onclick="createEnvironment()">创建</button>
                </div>
            </div>
            
            <div class="form-group">
                <label>环境列表:</label>
                <ul id="envList" class="env-list"></ul>
            </div>

            <div class="form-group" style="border-top: 2px solid #dc3545; padding-top: 15px; margin-top: 20px;">
                <label style="color: #dc3545; font-weight: bold;">⚠️ 管理员操作 (危险操作):</label>
                <p style="color: #666; font-size: 14px; margin: 5px 0;">这些操作会删除所有环境数据，请谨慎使用！</p>
                <div style="display: flex; gap: 10px; margin-bottom: 15px;">
                    <button class="btn" style="background: #dc3545; border-color: #dc3545;" onclick="clearAllEnvironments()">清除所有环境数据</button>
                    <button class="btn" style="background: #fd7e14; border-color: #fd7e14;" onclick="rebuildEnvironmentIndex()">重建环境索引</button>
                    <button class="btn btn-secondary" onclick="loadEnvironments()">刷新环境列表</button>
                </div>
            </div>

            <div id="envVariables" style="display: none;">
                <h3>环境变量管理</h3>

                <!-- 前置URL管理 -->
                <div class="form-group" style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin-bottom: 20px; border-left: 4px solid #007bff;">
                    <label style="font-weight: bold; color: #007bff;">前置URL (baseUrl):</label>
                    <p style="color: #666; font-size: 14px; margin: 5px 0;">设置环境的基础URL，在请求中可以使用 {{baseUrl}} 引用</p>
                    <div style="display: flex; gap: 10px; align-items: center;">
                        <input type="text" id="envBaseUrl" placeholder="例如: http://192.168.450.143:8080"
                               style="flex: 1; padding: 8px 12px; border: 2px solid #007bff; border-radius: 4px;"
                               value="http://192.168.450.143:8080">
                        <button class="btn" onclick="updateBaseUrl()" style="background: #007bff;">更新前置URL</button>
                    </div>
                    <div style="margin-top: 8px;">
                        <small style="color: #28a745; font-weight: bold;">
                            当前前置URL: <span id="currentBaseUrl">未设置</span>
                        </small>
                    </div>
                </div>

                <!-- 其他环境变量 -->
                <div class="form-group">
                    <label>其他环境变量:</label>
                    <div class="variable-list" id="variableList"></div>
                    <div class="form-group">
                        <label>添加新变量:</label>
                        <div class="variable-item">
                            <input type="text" id="newVarKey" placeholder="变量名">
                            <input type="text" id="newVarValue" placeholder="变量值">
                            <button class="btn btn-small" onclick="addVariable()">添加</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 全局变量管理标签页 -->
        <div id="globals" class="tab-content">
            <div class="form-group">
                <label>全局变量管理:</label>
                <p style="color: #666; font-size: 14px;">全局变量在所有环境中都可以使用，优先级低于环境变量</p>
            </div>

            <div class="form-group">
                <label>添加新的全局变量:</label>
                <div class="variable-item">
                    <input type="text" id="newGlobalKey" placeholder="变量名">
                    <input type="text" id="newGlobalValue" placeholder="变量值">
                    <button class="btn btn-small" onclick="addGlobalVariable()">添加</button>
                </div>
            </div>

            <div class="form-group">
                <label>批量操作:</label>
                <div style="display: flex; gap: 10px; margin-bottom: 15px;">
                    <button class="btn btn-small" onclick="loadGlobalVariables()">刷新列表</button>
                    <button class="btn btn-small" onclick="exportGlobalVariables()">导出JSON</button>
                    <button class="btn btn-small" onclick="showImportDialog()">导入JSON</button>
                    <button class="btn btn-small btn-secondary" onclick="clearAllGlobalVariables()">清空所有</button>
                </div>
            </div>

            <div id="importDialog" style="display: none; margin-bottom: 15px;">
                <div class="form-group">
                    <label>导入全局变量 (JSON格式):</label>
                    <textarea id="importJson" placeholder='{"key1": "value1", "key2": "value2"}'></textarea>
                    <div style="margin-top: 10px;">
                        <button class="btn btn-small" onclick="importGlobalVariables()">导入</button>
                        <button class="btn btn-small btn-secondary" onclick="hideImportDialog()">取消</button>
                    </div>
                </div>
            </div>

            <div class="form-group">
                <label>全局变量列表:</label>
                <div id="globalStats" style="margin-bottom: 10px; padding: 10px; background: #f8f9fa; border-radius: 4px; font-size: 14px;"></div>
                <div class="variable-list" id="globalVariableList"></div>
            </div>
        </div>

        <!-- 请求历史标签页 -->
        <div id="requests" class="tab-content">
            <div class="form-group">
                <label>请求历史:</label>
                <p style="color: #666; font-size: 14px;">查看和管理历史请求记录，支持重新执行、编辑和分类管理</p>
            </div>

            <div class="form-group">
                <label>快速操作:</label>
                <div style="display: flex; gap: 10px; margin-bottom: 15px;">
                    <button class="btn" onclick="showSaveCurrentRequestDialog()">保存当前请求</button>
                    <button class="btn btn-secondary" onclick="loadHttpRequests()">刷新历史</button>
                    <button class="btn btn-secondary" onclick="showRequestStats()">历史统计</button>
                </div>
            </div>

            <div class="form-group">
                <label>搜索和筛选:</label>
                <div style="display: flex; gap: 10px; margin-bottom: 15px;">
                    <input type="text" id="requestSearch" placeholder="搜索历史请求" style="flex: 1;">
                    <select id="requestCategoryFilter" style="flex: 1;">
                        <option value="">所有分类</option>
                        <option value="API">API</option>
                        <option value="Auth">认证</option>
                        <option value="Test">测试</option>
                        <option value="自动保存">自动保存</option>
                    </select>
                    <button class="btn btn-small" onclick="searchHttpRequests()">搜索</button>
                </div>
            </div>

            <div class="form-group">
                <label id="requestListLabel">历史请求列表:</label>
                <div id="requestStats" style="margin-bottom: 10px; padding: 10px; background: #f8f9fa; border-radius: 4px; font-size: 14px;"></div>
                <div id="requestList" class="script-grid"></div>
            </div>
        </div>

        <!-- 脚本管理标签页 -->
        <div id="scripts" class="tab-content">
            <div class="form-group">
                <label>脚本管理:</label>
                <p style="color: #666; font-size: 14px;">管理前置操作脚本和后置操作脚本，支持创建、编辑、删除和分类管理</p>
            </div>

            <div class="form-group">
                <label>脚本类型:</label>
                <div style="display: flex; gap: 10px; margin-bottom: 15px;">
                    <button class="btn" id="preRequestTab" onclick="switchScriptType('PRE_REQUEST')">前置操作脚本</button>
                    <button class="btn btn-secondary" id="testTab" onclick="switchScriptType('TEST')">后置操作脚本</button>
                </div>
            </div>

            <div class="form-group">
                <label>创建新脚本:</label>
                <div style="display: flex; gap: 10px; margin-bottom: 15px;">
                    <input type="text" id="newScriptName" placeholder="脚本名称" style="flex: 1;">
                    <input type="text" id="newScriptCategory" placeholder="分类 (可选)" style="flex: 1;">
                    <button class="btn" onclick="showCreateScriptDialog()">创建脚本</button>
                </div>
            </div>

            <div class="form-group">
                <label>搜索和筛选:</label>
                <div style="display: flex; gap: 10px; margin-bottom: 15px;">
                    <input type="text" id="scriptSearch" placeholder="搜索脚本名称" style="flex: 1;">
                    <select id="categoryFilter" style="flex: 1;">
                        <option value="">所有分类</option>
                        <option value="authentication">认证</option>
                        <option value="validation">验证</option>
                        <option value="utility">工具</option>
                        <option value="setup">设置</option>
                        <option value="cleanup">清理</option>
                    </select>
                    <button class="btn btn-small" onclick="searchScripts()">搜索</button>
                    <button class="btn btn-small" onclick="loadScripts()">刷新</button>
                </div>
            </div>

            <div class="form-group">
                <label id="scriptListLabel">前置操作脚本列表:</label>
                <div id="scriptStats" style="margin-bottom: 10px; padding: 10px; background: #f8f9fa; border-radius: 4px; font-size: 14px;"></div>
                <div id="scriptList" class="script-grid"></div>
            </div>
        </div>
    </div>

    <!-- 操作选择器模态框 -->
    <div id="actionSelector" class="script-selector">
        <div class="script-selector-content">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                <h3 id="actionSelectorTitle">添加操作</h3>
                <button class="btn btn-secondary" onclick="closeActionSelector()">关闭</button>
            </div>

            <div class="form-group">
                <label>操作类型:</label>
                <div style="display: flex; gap: 10px; margin-bottom: 15px;">
                    <button class="btn" id="scriptActionTab" onclick="switchActionType('script')">执行脚本</button>
                    <button class="btn btn-secondary" id="waitActionTab" onclick="switchActionType('wait')">等待时间</button>
                    <button class="btn btn-secondary" id="httpActionTab" onclick="switchActionType('http')">HTTP请求</button>
                </div>
            </div>

            <!-- 脚本选择界面 -->
            <div id="scriptActionPanel">
                <div style="margin-bottom: 15px;">
                    <input type="text" id="selectorSearch" placeholder="搜索脚本..." style="width: 100%;">
                </div>
                <div id="selectorScriptList" class="script-grid"></div>
            </div>

            <!-- 等待时间配置界面 -->
            <div id="waitActionPanel" style="display: none;">
                <div class="form-group">
                    <label>等待时间 (毫秒):</label>
                    <input type="number" id="waitTime" placeholder="例如: 1000 (1秒)" min="0" max="300000" style="width: 100%;">
                </div>
                <div class="form-group">
                    <label>操作描述 (可选):</label>
                    <input type="text" id="waitDescription" placeholder="例如: 等待服务器处理" style="width: 100%;">
                </div>
            </div>

            <!-- HTTP请求配置界面 -->
            <div id="httpActionPanel" style="display: none;">
                <div class="form-group">
                    <label>请求名称:</label>
                    <input type="text" id="httpActionName" placeholder="例如: 获取用户Token" style="width: 100%;">
                </div>

                <div class="form-group">
                    <label>请求方法:</label>
                    <select id="httpMethod" style="width: 100%;">
                        <option value="GET">GET</option>
                        <option value="POST">POST</option>
                        <option value="PUT">PUT</option>
                        <option value="DELETE">DELETE</option>
                        <option value="PATCH">PATCH</option>
                        <option value="HEAD">HEAD</option>
                        <option value="OPTIONS">OPTIONS</option>
                    </select>
                </div>

                <div class="form-group">
                    <label>请求URL (支持{{变量}}):</label>
                    <input type="text" id="httpUrl" placeholder="例如: {{baseUrl}}/auth/login" style="width: 100%;">
                </div>

                <div class="form-group">
                    <label>请求头 (JSON格式，可选):</label>
                    <textarea id="httpHeaders" placeholder='{"Content-Type": "application/json", "Authorization": "Bearer {{token}}"}' style="width: 100%; height: 80px;"></textarea>
                </div>

                <div class="form-group">
                    <label>请求体 (支持{{变量}}，可选):</label>
                    <textarea id="httpBody" placeholder='{"username": "{{username}}", "password": "{{password}}"}' style="width: 100%; height: 80px;"></textarea>
                </div>

                <div class="form-group">
                    <label>响应映射 (JSONPath → 环境变量):</label>
                    <div style="margin-bottom: 10px;">
                        <button class="btn btn-secondary btn-small" onclick="addResponseMapping()">添加映射</button>
                        <button class="btn btn-secondary btn-small" onclick="clearResponseMappings()">清空</button>
                    </div>
                    <div id="responseMappingContainer" class="params-container">
                        <div class="param-header">
                            <span style="width: 35%; display: inline-block; font-weight: bold;">JSONPath表达式</span>
                            <span style="width: 25%; display: inline-block; font-weight: bold;">环境变量名</span>
                            <span style="width: 20%; display: inline-block; font-weight: bold;">必需</span>
                            <span style="width: 20%; display: inline-block; font-weight: bold;">操作</span>
                        </div>
                        <div id="responseMappingList"></div>
                    </div>
                </div>

                <div class="form-group">
                    <label>操作描述 (可选):</label>
                    <input type="text" id="httpDescription" placeholder="例如: 登录获取访问令牌" style="width: 100%;">
                </div>
            </div>

            <div style="margin-top: 20px; text-align: right;">
                <button class="btn" onclick="confirmActionSelection()">确认添加</button>
                <button class="btn btn-secondary" onclick="closeActionSelector()">取消</button>
            </div>
        </div>
    </div>

    <!-- 脚本选择器模态框 (保留用于脚本管理页面) -->
    <div id="scriptSelector" class="script-selector">
        <div class="script-selector-content">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                <h3 id="selectorTitle">选择脚本</h3>
                <button class="btn btn-secondary" onclick="closeScriptSelector()">关闭</button>
            </div>

            <div style="margin-bottom: 15px;">
                <input type="text" id="selectorSearch2" placeholder="搜索脚本..." style="width: 100%;">
            </div>

            <div id="selectorScriptList2" class="script-grid"></div>

            <div style="margin-top: 20px; text-align: right;">
                <button class="btn" onclick="confirmScriptSelection()">确认选择</button>
                <button class="btn btn-secondary" onclick="closeScriptSelector()">取消</button>
            </div>
        </div>
    </div>

    <!-- 创建/编辑脚本模态框 -->
    <div id="scriptEditor" class="script-selector" style="display: none;">
        <div class="script-selector-content">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                <h3 id="editorTitle">创建脚本</h3>
                <button class="btn btn-secondary" onclick="closeScriptEditor()">关闭</button>
            </div>

            <div class="form-group">
                <label>脚本名称:</label>
                <input type="text" id="editorScriptName" style="width: 100%;">
            </div>

            <div class="form-group">
                <label>脚本描述:</label>
                <input type="text" id="editorScriptDescription" style="width: 100%;">
            </div>

            <div class="form-group">
                <label>分类:</label>
                <select id="editorScriptCategory" style="width: 100%;">
                    <option value="">选择分类</option>
                    <option value="authentication">认证</option>
                    <option value="validation">验证</option>
                    <option value="utility">工具</option>
                    <option value="setup">设置</option>
                    <option value="cleanup">清理</option>
                </select>
            </div>

            <div class="form-group">
                <label>脚本内容:</label>
                <textarea id="editorScriptContent" style="width: 100%; height: 300px; font-family: monospace;"></textarea>
            </div>

            <div style="margin-top: 20px; text-align: right;">
                <button class="btn" onclick="saveScript()">保存脚本</button>
                <button class="btn btn-secondary" onclick="closeScriptEditor()">取消</button>
            </div>
        </div>
    </div>

    <!-- 保存请求模态框 -->
    <div id="saveRequestDialog" class="script-selector" style="display: none;">
        <div class="script-selector-content">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                <h3 id="saveRequestTitle">保存请求到历史</h3>
                <button class="btn btn-secondary" onclick="closeSaveRequestDialog()">关闭</button>
            </div>

            <div class="form-group">
                <label>请求名称:</label>
                <input type="text" id="saveRequestName" style="width: 100%;" placeholder="例如: 获取用户信息">
            </div>

            <div class="form-group">
                <label>请求描述:</label>
                <input type="text" id="saveRequestDescription" style="width: 100%;" placeholder="例如: 根据用户ID获取用户详细信息">
            </div>

            <div class="form-group">
                <label>分类:</label>
                <select id="saveRequestCategory" style="width: 100%;">
                    <option value="">选择分类</option>
                    <option value="API">API</option>
                    <option value="Auth">认证</option>
                    <option value="Test">测试</option>
                    <option value="User">用户管理</option>
                    <option value="Data">数据操作</option>
                </select>
            </div>

            <div class="form-group">
                <label>标签 (用逗号分隔):</label>
                <input type="text" id="saveRequestTags" style="width: 100%;" placeholder="例如: user, api, get">
            </div>

            <div style="margin-top: 20px; text-align: right;">
                <button class="btn" onclick="saveCurrentRequest()">保存请求</button>
                <button class="btn btn-secondary" onclick="closeSaveRequestDialog()">取消</button>
            </div>
        </div>
    </div>

    <!-- 请求详情模态框 -->
    <div id="requestDetailDialog" class="script-selector" style="display: none;">
        <div class="script-selector-content">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                <h3 id="requestDetailTitle">历史请求详情</h3>
                <button class="btn btn-secondary" onclick="closeRequestDetailDialog()">关闭</button>
            </div>

            <div id="requestDetailContent">
                <!-- 请求详情内容将在这里动态生成 -->
            </div>

            <div style="margin-top: 20px; text-align: right;">
                <button class="btn" onclick="loadRequestToForm()">加载到表单</button>
                <button class="btn btn-secondary" onclick="editCurrentRequest()">编辑请求</button>
                <button class="btn btn-secondary" onclick="duplicateCurrentRequest()">复制请求</button>
                <button class="btn btn-secondary" onclick="closeRequestDetailDialog()">关闭</button>
            </div>
        </div>
    </div>

    <!-- 保存HTTP请求模态框 -->
    <div id="saveHttpRequestDialog" class="script-selector" style="display: none;">
        <div class="script-selector-content">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                <h3>保存HTTP请求</h3>
                <button class="btn btn-secondary" onclick="closeSaveHttpRequestDialog()">关闭</button>
            </div>

            <div class="form-group">
                <label>请求名称:</label>
                <input type="text" id="httpRequestName" placeholder="例如: 获取用户列表" style="width: 100%;">
            </div>

            <div class="form-group">
                <label>请求分类:</label>
                <select id="httpRequestCategory" style="width: 100%;">
                    <option value="API">API</option>
                    <option value="Auth">认证</option>
                    <option value="Test">测试</option>
                    <option value="User">用户管理</option>
                    <option value="Data">数据操作</option>
                </select>
            </div>

            <div class="form-group">
                <label>描述 (可选):</label>
                <textarea id="httpRequestDescription" placeholder="请求的详细描述..." style="width: 100%; height: 80px;"></textarea>
            </div>

            <div style="text-align: right; margin-top: 20px;">
                <button class="btn btn-secondary" onclick="closeSaveHttpRequestDialog()">取消</button>
                <button class="btn" onclick="confirmSaveHttpRequest()">保存</button>
            </div>
        </div>
    </div>

    <!-- 保存的HTTP请求管理模态框 -->
    <div id="savedHttpRequestsDialog" class="script-selector" style="display: none;">
        <div class="script-selector-content">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                <h3>保存的HTTP请求</h3>
                <button class="btn btn-secondary" onclick="closeSavedHttpRequestsDialog()">关闭</button>
            </div>

            <div class="form-group">
                <label>搜索和筛选:</label>
                <div style="display: flex; gap: 10px; margin-bottom: 15px;">
                    <input type="text" id="savedRequestSearch" placeholder="搜索请求名称" style="flex: 1;">
                    <select id="savedRequestCategoryFilter" style="flex: 1;">
                        <option value="">所有分类</option>
                        <option value="API">API</option>
                        <option value="Auth">认证</option>
                        <option value="Test">测试</option>
                        <option value="User">用户管理</option>
                        <option value="Data">数据操作</option>
                    </select>
                    <button class="btn btn-small" onclick="searchSavedHttpRequests()">搜索</button>
                    <button class="btn btn-small" onclick="loadSavedHttpRequests()">刷新</button>
                </div>
            </div>

            <div class="form-group">
                <label>请求列表:</label>
                <div id="savedHttpRequestsList" class="script-grid"></div>
            </div>
        </div>
    </div>

    <!-- 执行HTTP请求对话框 -->
    <div id="executeHttpRequestDialog" class="script-selector" style="display: none;">
        <div class="script-selector-content">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                <h3 id="executeRequestTitle">执行HTTP请求</h3>
                <button class="btn btn-secondary" onclick="closeExecuteHttpRequestDialog()">关闭</button>
            </div>

            <div class="form-group">
                <label>请求信息:</label>
                <div id="executeRequestInfo" style="padding: 10px; background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 4px; margin-bottom: 15px;">
                    <p><strong id="executeRequestMethod">GET</strong> <span id="executeRequestUrl">{{baseUrl}}/api/test</span></p>
                    <p style="color: #666; font-size: 14px;" id="executeRequestDescription">请求描述</p>
                </div>
            </div>

            <div class="form-group">
                <label>Path参数 (可选):</label>
                <div style="margin-bottom: 10px;">
                    <button class="btn btn-secondary btn-small" onclick="addExecutePathParam()">添加参数</button>
                    <button class="btn btn-secondary btn-small" onclick="clearExecutePathParams()">清空</button>
                </div>
                <div id="executePathParamsContainer" class="params-container">
                    <div class="param-header">
                        <span style="width: 40%; display: inline-block; font-weight: bold;">参数名</span>
                        <span style="width: 40%; display: inline-block; font-weight: bold;">参数值</span>
                        <span style="width: 20%; display: inline-block; font-weight: bold;">操作</span>
                    </div>
                    <div id="executePathParamsList"></div>
                </div>
            </div>

            <div class="form-group">
                <label>Query参数 (可选):</label>
                <div style="margin-bottom: 10px;">
                    <button class="btn btn-secondary btn-small" onclick="addExecuteQueryParam()">添加参数</button>
                    <button class="btn btn-secondary btn-small" onclick="clearExecuteQueryParams()">清空</button>
                </div>
                <div id="executeQueryParamsContainer" class="params-container">
                    <div class="param-header">
                        <span style="width: 40%; display: inline-block; font-weight: bold;">参数名</span>
                        <span style="width: 40%; display: inline-block; font-weight: bold;">参数值</span>
                        <span style="width: 20%; display: inline-block; font-weight: bold;">操作</span>
                    </div>
                    <div id="executeQueryParamsList"></div>
                </div>
            </div>

            <div class="form-group">
                <label>请求头 (JSON格式，可选):</label>
                <textarea id="executeHeaders" placeholder='{"Content-Type": "application/json", "Authorization": "Bearer {{token}}"}' style="width: 100%; height: 80px;"></textarea>
            </div>

            <div class="form-group">
                <label>请求体 (支持{{变量}}，可选):</label>
                <textarea id="executeBody" placeholder='{"key": "{{value}}", "data": "test"}' style="width: 100%; height: 100px;"></textarea>
            </div>

            <div style="text-align: right; margin-top: 20px;">
                <button class="btn btn-secondary" onclick="closeExecuteHttpRequestDialog()">取消</button>
                <button class="btn" onclick="confirmExecuteHttpRequest()">执行请求</button>
            </div>
        </div>
    </div>

    <script>
        let currentEnvironmentId = null;
        let currentEnvironmentName = null;

        // 脚本管理相关变量
        let currentScriptType = 'PRE_REQUEST';
        let selectedPreRequestActions = [];  // 前置操作列表 (包含脚本和等待时间)
        let selectedPostActions = [];        // 后置操作列表 (包含脚本和等待时间)
        let selectedPreRequestScripts = [];  // 保留兼容性
        let selectedTestScripts = [];        // 保留兼容性
        let allScripts = [];
        let currentEditingScript = null;
        let scriptSelectorType = null;
        let actionSelectorType = null;
        let currentActionType = 'script';

        // 请求历史相关变量
        let allHttpRequests = [];
        let currentEditingRequest = null;
        let currentViewingRequest = null;

        // 参数管理相关变量
        let pathParams = [];
        let queryParams = [];

        // 响应映射管理相关变量
        let responseMappings = [];

        // 执行HTTP请求相关变量
        let executePathParams = [];
        let executeQueryParams = [];
        let currentExecuteRequestId = null;

        // ==================== 标签页管理功能 ====================

        // 显示指定标签页
        function showTab(tabName) {
            // 隐藏所有标签页内容
            const tabContents = document.querySelectorAll('.tab-content');
            tabContents.forEach(content => {
                content.style.display = 'none';
            });

            // 移除所有标签的active类
            const tabs = document.querySelectorAll('.tab');
            tabs.forEach(tab => {
                tab.classList.remove('active');
            });

            // 显示指定的标签页内容
            const targetContent = document.getElementById(tabName);
            if (targetContent) {
                targetContent.style.display = 'block';
            }

            // 激活对应的标签
            const targetTab = document.querySelector(`.tab[onclick="showTab('${tabName}')"]`);
            if (targetTab) {
                targetTab.classList.add('active');
            }

            console.log('切换到标签页:', tabName);
        }

        // 页面加载时初始化
        window.onload = function() {
            loadEnvironments();
            updateCurrentEnvironmentDisplay();
            updateSelectedActionsDisplay();

            // 初始化参数列表
            renderPathParams();
            renderQueryParams();

            // 初始化响应映射列表
            renderResponseMappings();

            // 添加一个默认的示例参数
            addPathParam('userId', '{{userId}}');
            addQueryParam('status', 'active');

            // 添加一个默认的响应映射示例
            addResponseMapping('$.data.token', 'authToken', true);
        };
        
        // 标签页切换
        function showTab(tabName) {
            // 隐藏所有标签页内容
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // 显示选中的标签页
            document.getElementById(tabName).classList.add('active');
            event.target.classList.add('active');
            
            if (tabName === 'environment') {
                loadEnvironments();
            } else if (tabName === 'globals') {
                loadGlobalVariables();
            } else if (tabName === 'scripts') {
                loadScripts();
            } else if (tabName === 'requests') {
                loadHttpRequests();
            }
        }
        
        // 发送HTTP请求
        async function sendRequest() {
            // 提取脚本ID用于后端兼容性
            const preRequestScriptIds = selectedPreRequestActions
                .filter(action => action.type === 'script')
                .map(action => action.script.id);
            const testScriptIds = selectedPostActions
                .filter(action => action.type === 'script')
                .map(action => action.script.id);

            // 处理参数
            const pathParamsObj = paramsArrayToObject(pathParams);
            const queryParamsObj = paramsArrayToObject(queryParams);

            // 构建最终URL
            const baseUrl = document.getElementById('url').value;
            const finalUrl = buildFinalUrl(baseUrl, pathParamsObj, queryParamsObj);

            const request = {
                method: document.getElementById('method').value,
                url: finalUrl,
                headers: JSON.parse(document.getElementById('headers').value || '{}'),
                body: document.getElementById('body').value,
                preRequestScript: document.getElementById('preScript').value,
                testScript: document.getElementById('testScript').value,
                preRequestScriptIds: preRequestScriptIds,
                testScriptIds: testScriptIds,
                preRequestActions: selectedPreRequestActions,  // 新增：完整的前置操作列表
                postActions: selectedPostActions,              // 新增：完整的后置操作列表
                environmentId: currentEnvironmentId,
                // 新增：参数信息
                pathParams: pathParamsObj,
                queryParams: queryParamsObj,
                originalUrl: baseUrl  // 保存原始URL用于调试
            };

            // 调试信息
            console.log('=== 发送请求调试信息 ===');
            console.log('原始URL:', baseUrl);
            console.log('Path参数:', pathParamsObj);
            console.log('Query参数:', queryParamsObj);
            console.log('最终URL:', finalUrl);
            console.log('Request object:', request);
            console.log('前置操作数量:', selectedPreRequestActions.length);
            console.log('后置操作数量:', selectedPostActions.length);
            console.log('前置操作详情:', selectedPreRequestActions);
            console.log('后置操作详情:', selectedPostActions);
            console.log('Request body:', request.body);
            console.log('Request body length:', request.body ? request.body.length : 0);
            console.log('Request body type:', typeof request.body);

            const payload = { params: { request: request } };
            console.log('Full payload:', payload);

            try {
                const response = await fetch('/apiPostman/requests/execute', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(payload)
                });

                const result = await response.json();
                console.log('Response result:', result);
                displayResponse(result.data || result);
            } catch (error) {
                console.error('Request error:', error);
                displayResponse({
                    statusCode: 0,
                    statusText: 'Error',
                    body: 'Request failed: ' + error.message,
                    responseTime: 0
                });
            }
        }
        
        // 显示响应结果
        function displayResponse(response) {
            const responseDiv = document.getElementById('response');
            const responseContent = document.getElementById('responseContent');

            // 格式化响应显示
            let displayData = response;
            if (response && typeof response === 'object') {
                // 如果是统一响应格式，提取实际数据
                if (response.data !== undefined && response.msgCode !== undefined) {
                    displayData = {
                        status: `${response.msgCode} - ${response.message}`,
                        data: response.data
                    };
                }
            }

            responseContent.textContent = JSON.stringify(displayData, null, 2);
            responseDiv.style.display = 'block';

            // 滚动到响应区域
            responseDiv.scrollIntoView({ behavior: 'smooth' });
        }
        
        // 清空响应
        function clearResponse() {
            document.getElementById('response').style.display = 'none';
        }

        // 通用API调用函数
        async function callAPI(endpoint, params = {}, showSuccess = true) {
            try {
                const response = await fetch(endpoint, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ params: params })
                });

                const result = await response.json();

                if (response.ok && result.msgCode === 200) {
                    if (showSuccess && result.message) {
                        showNotification(result.message, 'success');
                    }
                    return result.data;
                } else {
                    const errorMsg = result.message || `HTTP ${response.status}: ${response.statusText}`;
                    showNotification(errorMsg, 'error');
                    throw new Error(errorMsg);
                }
            } catch (error) {
                const errorMsg = error.message || '网络请求失败';
                showNotification(errorMsg, 'error');
                throw error;
            }
        }

        // 显示通知消息
        function showNotification(message, type = 'info') {
            // 创建通知元素
            const notification = document.createElement('div');
            notification.className = `notification notification-${type}`;
            notification.textContent = message;

            // 添加样式
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 12px 20px;
                border-radius: 4px;
                color: white;
                font-weight: bold;
                z-index: 1000;
                max-width: 300px;
                word-wrap: break-word;
                ${type === 'success' ? 'background-color: #28a745;' : ''}
                ${type === 'error' ? 'background-color: #dc3545;' : ''}
                ${type === 'info' ? 'background-color: #17a2b8;' : ''}
            `;

            document.body.appendChild(notification);

            // 3秒后自动移除
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 3000);
        }
        
        // 加载环境列表
        async function loadEnvironments() {
            try {
                const environments = await callAPI('/apiPostman/environments/list', {}, false);

                const envList = document.getElementById('envList');
                envList.innerHTML = '';

                if (!environments || environments.length === 0) {
                    envList.innerHTML = '<li style="text-align: center; color: #666; padding: 20px;">暂无环境，请创建一个新环境</li>';
                    return;
                }

                environments.forEach(env => {
                    const li = document.createElement('li');
                    li.className = 'env-item';
                    if (currentEnvironmentId === env.id) {
                        li.classList.add('active');
                    }

                    const variableCount = Object.keys(env.variables || {}).length;
                    li.innerHTML = `
                        <span>${env.name} (${variableCount} 个变量)</span>
                        <div>
                            <button class="btn btn-small" onclick="selectEnvironment('${env.id}', '${env.name}')">选择</button>
                            <button class="btn btn-small" onclick="editEnvironment('${env.id}')">编辑</button>
                            <button class="btn btn-small btn-secondary" onclick="deleteEnvironment('${env.id}', '${env.name}')">删除</button>
                        </div>
                    `;

                    envList.appendChild(li);
                });

                // 更新当前环境显示
                updateCurrentEnvironmentDisplay();
            } catch (error) {
                console.error('Failed to load environments:', error);
                const envList = document.getElementById('envList');
                envList.innerHTML = '<li style="text-align: center; color: #dc3545; padding: 20px;">加载环境失败，请刷新重试</li>';
            }
        }
        
        // 创建环境
        async function createEnvironment() {
            const name = document.getElementById('newEnvName').value.trim();
            if (!name) {
                showNotification('请输入环境名称', 'error');
                return;
            }

            try {
                const newEnv = await callAPI('/apiPostman/environments', { name: name });
                document.getElementById('newEnvName').value = '';
                await loadEnvironments();

                // 自动选择新创建的环境
                if (newEnv && newEnv.id) {
                    selectEnvironment(newEnv.id, newEnv.name);
                }
            } catch (error) {
                console.error('Failed to create environment:', error);
            }
        }

        // 选择环境（替代原来的激活环境）
        async function selectEnvironment(id, name) {
            try {
                console.log('=== 选择环境调试信息 ===');
                console.log('选择的环境ID:', id);
                console.log('选择的环境名称:', name);
                console.log('之前的currentEnvironmentId:', currentEnvironmentId);

                currentEnvironmentId = id;
                currentEnvironmentName = name || id;

                console.log('更新后的currentEnvironmentId:', currentEnvironmentId);
                console.log('更新后的currentEnvironmentName:', currentEnvironmentName);

                await loadEnvironments();
                updateCurrentEnvironmentDisplay();
                showNotification(`已选择环境: ${currentEnvironmentName}`, 'success');
            } catch (error) {
                console.error('选择环境失败:', error);
                showNotification('选择环境失败: ' + error.message, 'error');
            }
        }

        // 更新当前环境显示
        function updateCurrentEnvironmentDisplay() {
            const displays = document.querySelectorAll('.current-env-display');
            displays.forEach(display => {
                if (currentEnvironmentId) {
                    display.textContent = `当前环境: ${currentEnvironmentName || currentEnvironmentId}`;
                    display.style.color = '#28a745';
                } else {
                    display.textContent = '未选择环境';
                    display.style.color = '#dc3545';
                }
            });
        }
        
        // 编辑环境
        async function editEnvironment(id) {
            try {
                const env = await callAPI('/apiPostman/environments/get', { id: id }, false);
                currentEnvironmentId = id;
                showEnvironmentVariables(env);

                // 切换到环境变量标签页
                showTab('environments');
            } catch (error) {
                console.error('Failed to get environment:', error);
            }
        }
        
        // 显示环境变量编辑界面
        function showEnvironmentVariables(env) {
            const envVariables = document.getElementById('envVariables');
            const variableList = document.getElementById('variableList');

            // 更新前置URL显示 (从环境变量中读取baseUrl)
            const baseUrlInput = document.getElementById('envBaseUrl');
            const currentBaseUrlSpan = document.getElementById('currentBaseUrl');

            const baseUrlFromVariables = env.variables && env.variables.baseUrl;
            if (baseUrlFromVariables) {
                baseUrlInput.value = baseUrlFromVariables;
                currentBaseUrlSpan.textContent = baseUrlFromVariables;
                currentBaseUrlSpan.style.color = '#28a745';
            } else if (env.baseUrl) {
                // 兼容旧的baseUrl字段
                baseUrlInput.value = env.baseUrl;
                currentBaseUrlSpan.textContent = env.baseUrl;
                currentBaseUrlSpan.style.color = '#28a745';
            } else {
                baseUrlInput.value = 'http://localhost:8080';
                currentBaseUrlSpan.textContent = '未设置';
                currentBaseUrlSpan.style.color = '#666';
            }

            variableList.innerHTML = '';

            Object.entries(env.variables).forEach(([key, value]) => {
                // 跳过baseUrl，因为它有专门的显示区域
                if (key === 'baseUrl') {
                    return;
                }

                const div = document.createElement('div');
                div.className = 'variable-item';
                div.innerHTML = `
                    <input type="text" value="${key}" readonly>
                    <input type="text" value="${value}" onchange="updateVariable('${key}', this.value)">
                    <button class="btn btn-small btn-secondary" onclick="removeVariable('${key}')">删除</button>
                `;
                variableList.appendChild(div);
            });

            envVariables.style.display = 'block';
        }

        // 更新前置URL (使用环境变量API)
        async function updateBaseUrl() {
            const baseUrl = document.getElementById('envBaseUrl').value.trim();

            console.log('=== 更新前置URL调试信息 ===');
            console.log('输入的baseUrl:', baseUrl);
            console.log('当前环境ID:', currentEnvironmentId);
            console.log('当前环境名称:', currentEnvironmentName);

            if (!baseUrl) {
                showNotification('请输入前置URL', 'error');
                return;
            }

            if (!currentEnvironmentId) {
                showNotification('请先选择一个环境', 'error');
                console.error('currentEnvironmentId为空，无法更新前置URL');
                return;
            }

            try {
                console.log('发送API请求，参数:', {
                    id: currentEnvironmentId,
                    key: 'baseUrl',
                    value: baseUrl
                });

                // 使用设置环境变量的API来设置baseUrl
                await callAPI('/apiPostman/environments/variables/set', {
                    id: currentEnvironmentId,
                    key: 'baseUrl',
                    value: baseUrl
                });

                // 更新显示的当前前置URL
                document.getElementById('currentBaseUrl').textContent = baseUrl;
                document.getElementById('currentBaseUrl').style.color = '#28a745';

                showNotification('前置URL更新成功', 'success');

                // 刷新环境变量显示
                const env = await callAPI('/apiPostman/environments/get', { id: currentEnvironmentId });
                if (env) {
                    showEnvironmentVariables(env);
                }
            } catch (error) {
                console.error('Failed to update base URL:', error);
                showNotification('更新前置URL失败: ' + (error.message || '未知错误'), 'error');
            }
        }

        // 清除所有环境数据
        async function clearAllEnvironments() {
            if (!confirm('⚠️ 警告：这将删除所有环境数据！\n\n包括：\n- 所有环境配置\n- 所有环境变量\n- 所有前置URL设置\n\n此操作不可恢复，确定要继续吗？')) {
                return;
            }

            if (!confirm('请再次确认：您真的要删除所有环境数据吗？')) {
                return;
            }

            try {
                showNotification('正在清除环境数据...', 'info');

                const result = await callAPI('/apiPostman/environments/admin/clear', {});

                if (result.cleared) {
                    showNotification('✅ 成功清除所有环境数据', 'success');

                    // 清空前端显示
                    document.getElementById('envList').innerHTML = '';
                    document.getElementById('envVariables').style.display = 'none';
                    currentEnvironmentId = null;
                    updateCurrentEnvironmentDisplay();

                    console.log('环境数据清除完成:', result);
                } else {
                    showNotification('❌ 清除环境数据失败', 'error');
                }
            } catch (error) {
                console.error('Failed to clear environments:', error);
                showNotification('清除环境数据失败: ' + error.message, 'error');
            }
        }

        // 重建环境索引
        async function rebuildEnvironmentIndex() {
            if (!confirm('确定要重建环境索引吗？\n\n这将：\n- 删除现有的环境索引\n- 创建新的环境索引\n- 确保索引结构是最新的')) {
                return;
            }

            try {
                showNotification('正在重建环境索引...', 'info');

                const result = await callAPI('/apiPostman/environments/admin/rebuild', {});

                if (result.rebuilt) {
                    showNotification('✅ 成功重建环境索引', 'success');
                    console.log('环境索引重建完成:', result);
                } else {
                    showNotification('❌ 重建环境索引失败', 'error');
                }
            } catch (error) {
                console.error('Failed to rebuild environment index:', error);
                showNotification('重建环境索引失败: ' + error.message, 'error');
            }
        }

        // 添加变量
        async function addVariable() {
            const key = document.getElementById('newVarKey').value.trim();
            const value = document.getElementById('newVarValue').value;

            if (!key) {
                showNotification('请输入变量名', 'error');
                return;
            }

            if (!currentEnvironmentId) {
                showNotification('请先选择一个环境', 'error');
                return;
            }

            try {
                await callAPI('/apiPostman/environments/variables/set', {
                    id: currentEnvironmentId,
                    key: key,
                    value: value
                });

                document.getElementById('newVarKey').value = '';
                document.getElementById('newVarValue').value = '';
                editEnvironment(currentEnvironmentId);
            } catch (error) {
                console.error('Failed to add variable:', error);
            }
        }
        
        // 更新变量
        async function updateVariable(key, value) {
            try {
                await fetch(`/apiPostman/environments/${currentEnvironmentId}/variables`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ key: key, value: value })
                });
            } catch (error) {
                alert('更新变量失败: ' + error.message);
            }
        }
        
        // 删除变量
        async function removeVariable(key) {
            if (!confirm(`确定要删除变量 "${key}" 吗？`)) {
                return;
            }

            try {
                await callAPI('/apiPostman/environments/variables/delete', {
                    id: currentEnvironmentId,
                    key: key
                });

                editEnvironment(currentEnvironmentId);
            } catch (error) {
                console.error('Failed to delete variable:', error);
            }
        }

        // 删除环境
        async function deleteEnvironment(id, name) {
            if (!confirm(`确定要删除环境 "${name || id}" 吗？此操作不可撤销！`)) {
                return;
            }

            try {
                await callAPI('/apiPostman/environments/delete', { id: id });

                if (currentEnvironmentId === id) {
                    currentEnvironmentId = null;
                    currentEnvironmentName = null;
                    document.getElementById('envVariables').style.display = 'none';
                    updateCurrentEnvironmentDisplay();
                }

                await loadEnvironments();
            } catch (error) {
                console.error('Failed to delete environment:', error);
            }
        }

        // HTML转义函数
        function escapeHtml(text) {
            if (typeof text !== 'string') return text;
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        // ==================== 全局变量管理函数 ====================

        // 加载全局变量列表
        async function loadGlobalVariables() {
            try {
                // 获取全局变量列表
                const response = await fetch('/apiPostman/globals/list', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ params: {} })
                });
                const result = await response.json();
                const globals = result.data || result;

                // 获取统计信息
                const statsResponse = await fetch('/apiPostman/globals/stats', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ params: {} })
                });
                const statsResult = await statsResponse.json();
                const stats = statsResult.data || statsResult;

                // 更新统计信息显示
                const statsDiv = document.getElementById('globalStats');
                statsDiv.innerHTML = `
                    <strong>统计信息:</strong>
                    共 ${stats.count} 个全局变量
                    ${stats.isEmpty ? '(列表为空)' : ''}
                `;

                // 更新变量列表
                const variableList = document.getElementById('globalVariableList');
                variableList.innerHTML = '';

                if (Object.keys(globals).length === 0) {
                    variableList.innerHTML = '<p style="color: #666; text-align: center; padding: 20px;">暂无全局变量</p>';
                    return;
                }

                Object.entries(globals).forEach(([key, value]) => {
                    const div = document.createElement('div');
                    div.className = 'variable-item';
                    div.innerHTML = `
                        <input type="text" value="${escapeHtml(key)}" readonly style="background: #f8f9fa;">
                        <input type="text" value="${escapeHtml(value)}" onchange="updateGlobalVariable('${escapeHtml(key)}', this.value)" placeholder="变量值">
                        <button class="btn btn-small btn-secondary" onclick="removeGlobalVariable('${escapeHtml(key)}')">删除</button>
                    `;
                    variableList.appendChild(div);
                });

            } catch (error) {
                console.error('Failed to load global variables:', error);
                showNotification('加载全局变量失败: ' + error.message, 'error');
            }
        }

        // 添加全局变量
        async function addGlobalVariable() {
            const key = document.getElementById('newGlobalKey').value.trim();
            const value = document.getElementById('newGlobalValue').value.trim();

            if (!key) {
                showNotification('请输入变量名', 'error');
                return;
            }

            try {
                await callAPI('/apiPostman/globals', { key: key, value: value });

                document.getElementById('newGlobalKey').value = '';
                document.getElementById('newGlobalValue').value = '';
                loadGlobalVariables();
            } catch (error) {
                console.error('Failed to add global variable:', error);
            }
        }

        // 更新全局变量
        async function updateGlobalVariable(key, value) {
            try {
                await callAPI('/apiPostman/globals', { key: key, value: value }, false);
                showNotification(`全局变量 "${key}" 更新成功`, 'success');
            } catch (error) {
                console.error('Failed to update global variable:', error);
                loadGlobalVariables(); // 重新加载以恢复原值
            }
        }

        // 删除全局变量
        async function removeGlobalVariable(key) {
            if (!confirm(`确定要删除全局变量 "${key}" 吗？`)) {
                return;
            }

            try {
                await callAPI('/apiPostman/globals/delete', { key: key });
                loadGlobalVariables();
            } catch (error) {
                console.error('Failed to delete global variable:', error);
            }
        }

        // 清空所有全局变量
        async function clearAllGlobalVariables() {
            if (!confirm('确定要清空所有全局变量吗？此操作不可撤销！')) {
                return;
            }

            try {
                await callAPI('/apiPostman/globals/clear', {});
                loadGlobalVariables();
            } catch (error) {
                console.error('Failed to clear global variables:', error);
            }
        }

        // 导出全局变量为JSON
        async function exportGlobalVariables() {
            try {
                const globals = await callAPI('/apiPostman/globals/list', {}, false);

                const jsonStr = JSON.stringify(globals, null, 2);

                // 创建下载链接
                const blob = new Blob([jsonStr], { type: 'application/json' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `global-variables-${new Date().toISOString().split('T')[0]}.json`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);

                showNotification('全局变量已导出为JSON文件', 'success');
            } catch (error) {
                console.error('Failed to export global variables:', error);
            }
        }

        // 显示导入对话框
        function showImportDialog() {
            document.getElementById('importDialog').style.display = 'block';
        }

        // 隐藏导入对话框
        function hideImportDialog() {
            document.getElementById('importDialog').style.display = 'none';
            document.getElementById('importJson').value = '';
        }

        // 导入全局变量
        async function importGlobalVariables() {
            const jsonStr = document.getElementById('importJson').value.trim();

            if (!jsonStr) {
                showNotification('请输入JSON数据', 'error');
                return;
            }

            try {
                const variables = JSON.parse(jsonStr);

                if (typeof variables !== 'object' || variables === null || Array.isArray(variables)) {
                    showNotification('JSON格式错误：必须是对象格式，如 {"key1": "value1", "key2": "value2"}', 'error');
                    return;
                }

                const result = await callAPI('/apiPostman/globals/batch', variables);

                hideImportDialog();
                loadGlobalVariables();
                showNotification(`成功导入 ${Object.keys(variables).length} 个全局变量`, 'success');
            } catch (error) {
                if (error instanceof SyntaxError) {
                    showNotification('JSON格式错误: ' + error.message, 'error');
                } else {
                    console.error('Failed to import global variables:', error);
                }
            }
        }

        // ==================== 脚本管理功能 ====================

        // 切换脚本类型
        function switchScriptType(type) {
            currentScriptType = type;

            // 更新按钮状态
            document.getElementById('preRequestTab').className = type === 'PRE_REQUEST' ? 'btn' : 'btn btn-secondary';
            document.getElementById('testTab').className = type === 'TEST' ? 'btn' : 'btn btn-secondary';

            // 更新标签
            document.getElementById('scriptListLabel').textContent =
                type === 'PRE_REQUEST' ? '前置操作脚本列表:' : '后置操作脚本列表:';

            loadScripts();
        }

        // 加载脚本列表
        async function loadScripts() {
            try {
                const result = await callAPI('/apiPostman/scripts/list', { type: currentScriptType });
                allScripts = result || [];
                displayScripts(allScripts);
                updateScriptStats();
            } catch (error) {
                console.error('Failed to load scripts:', error);
                showNotification('加载脚本列表失败', 'error');
            }
        }

        // 显示脚本列表
        function displayScripts(scripts) {
            const scriptList = document.getElementById('scriptList');
            scriptList.innerHTML = '';

            if (scripts.length === 0) {
                scriptList.innerHTML = '<p style="text-align: center; color: #666;">暂无脚本</p>';
                return;
            }

            scripts.forEach(script => {
                const scriptCard = document.createElement('div');
                scriptCard.className = 'script-card';
                scriptCard.innerHTML = `
                    <div class="script-title">${script.name}</div>
                    <div class="script-meta">
                        分类: ${script.category || '未分类'} |
                        使用次数: ${script.usageCount || 0} |
                        版本: ${script.version || 1}
                    </div>
                    <div class="script-preview">${(script.script || '').substring(0, 100)}${script.script && script.script.length > 100 ? '...' : ''}</div>
                    <div style="margin-top: 10px;">
                        <button class="btn btn-small" onclick="editScript('${script.id}')">编辑</button>
                        <button class="btn btn-small btn-secondary" onclick="deleteScript('${script.id}')">删除</button>
                        <button class="btn btn-small" onclick="duplicateScript('${script.id}')">复制</button>
                    </div>
                `;
                scriptList.appendChild(scriptCard);
            });
        }

        // 更新脚本统计信息
        async function updateScriptStats() {
            try {
                const stats = await callAPI('/apiPostman/scripts/stats', { type: currentScriptType });
                const statsDiv = document.getElementById('scriptStats');
                statsDiv.innerHTML = `
                    总计: ${stats.totalCount} 个脚本 |
                    激活: ${stats.activeCount} 个 |
                    类型: ${currentScriptType === 'PRE_REQUEST' ? '前置操作脚本' : '后置操作脚本'}
                `;
            } catch (error) {
                console.error('Failed to load script stats:', error);
            }
        }

        // 搜索脚本
        async function searchScripts() {
            const searchQuery = document.getElementById('scriptSearch').value.trim();
            const categoryFilter = document.getElementById('categoryFilter').value;

            try {
                let scripts = allScripts;

                if (searchQuery) {
                    const searchResult = await callAPI('/apiPostman/scripts/search', {
                        type: currentScriptType,
                        searchQuery: searchQuery
                    });
                    scripts = searchResult || [];
                }

                if (categoryFilter) {
                    const categoryResult = await callAPI('/apiPostman/scripts/category', {
                        type: currentScriptType,
                        category: categoryFilter
                    });
                    scripts = categoryResult || [];
                }

                if (searchQuery && categoryFilter) {
                    scripts = scripts.filter(script =>
                        script.name.toLowerCase().includes(searchQuery.toLowerCase()) &&
                        script.category === categoryFilter
                    );
                }

                displayScripts(scripts);
            } catch (error) {
                console.error('Failed to search scripts:', error);
                showNotification('搜索脚本失败', 'error');
            }
        }

        // 显示创建脚本对话框
        function showCreateScriptDialog() {
            const name = document.getElementById('newScriptName').value.trim();
            const category = document.getElementById('newScriptCategory').value.trim();

            if (!name) {
                showNotification('请输入脚本名称', 'error');
                return;
            }

            currentEditingScript = null;
            document.getElementById('editorTitle').textContent = '创建脚本';
            document.getElementById('editorScriptName').value = name;
            document.getElementById('editorScriptDescription').value = '';
            document.getElementById('editorScriptCategory').value = category;
            document.getElementById('editorScriptContent').value = getDefaultScriptContent();
            document.getElementById('scriptEditor').style.display = 'block';
        }

        // 获取默认脚本内容
        function getDefaultScriptContent() {
            if (currentScriptType === 'PRE_REQUEST') {
                return `// 前置操作脚本示例
pm.environment.set('timestamp', Date.now().toString());
console.log('Pre-request action script executed');`;
            } else {
                return `// 后置操作脚本示例
pm.test('Status code is 200', function () {
    pm.expect(pm.response.code()).to.equal(200);
});

pm.test('Response time is less than 1000ms', function () {
    pm.expect(pm.response.responseTime()).to.be.below(1000);
});`;
            }
        }

        // 编辑脚本
        async function editScript(scriptId) {
            try {
                const script = await callAPI('/apiPostman/scripts/get', {
                    id: scriptId,
                    type: currentScriptType
                });

                if (script) {
                    currentEditingScript = script;
                    document.getElementById('editorTitle').textContent = '编辑脚本';
                    document.getElementById('editorScriptName').value = script.name;
                    document.getElementById('editorScriptDescription').value = script.description || '';
                    document.getElementById('editorScriptCategory').value = script.category || '';
                    document.getElementById('editorScriptContent').value = script.script || '';
                    document.getElementById('scriptEditor').style.display = 'block';
                }
            } catch (error) {
                console.error('Failed to load script:', error);
                showNotification('加载脚本失败', 'error');
            }
        }

        // 保存脚本
        async function saveScript() {
            const name = document.getElementById('editorScriptName').value.trim();
            const description = document.getElementById('editorScriptDescription').value.trim();
            const category = document.getElementById('editorScriptCategory').value;
            const content = document.getElementById('editorScriptContent').value;

            if (!name) {
                showNotification('请输入脚本名称', 'error');
                return;
            }

            if (!content.trim()) {
                showNotification('请输入脚本内容', 'error');
                return;
            }

            try {
                if (currentEditingScript) {
                    // 更新脚本
                    await callAPI('/apiPostman/scripts/update', {
                        id: currentEditingScript.id,
                        name: name,
                        description: description,
                        category: category,
                        script: content,
                        type: currentScriptType
                    });
                    showNotification('脚本更新成功', 'success');
                } else {
                    // 创建脚本
                    await callAPI('/apiPostman/scripts/create', {
                        name: name,
                        description: description,
                        category: category,
                        script: content,
                        type: currentScriptType
                    });
                    showNotification('脚本创建成功', 'success');
                }

                closeScriptEditor();
                loadScripts();

                // 清空创建表单
                document.getElementById('newScriptName').value = '';
                document.getElementById('newScriptCategory').value = '';
            } catch (error) {
                console.error('Failed to save script:', error);
                showNotification('保存脚本失败', 'error');
            }
        }

        // 删除脚本
        async function deleteScript(scriptId) {
            if (!confirm('确定要删除这个脚本吗？')) {
                return;
            }

            try {
                await callAPI('/apiPostman/scripts/delete', {
                    id: scriptId,
                    type: currentScriptType
                });
                showNotification('脚本删除成功', 'success');
                loadScripts();
            } catch (error) {
                console.error('Failed to delete script:', error);
                showNotification('删除脚本失败', 'error');
            }
        }

        // 复制脚本
        async function duplicateScript(scriptId) {
            try {
                const script = await callAPI('/apiPostman/scripts/get', {
                    id: scriptId,
                    type: currentScriptType
                });

                if (script) {
                    await callAPI('/apiPostman/scripts/create', {
                        name: script.name + ' (副本)',
                        description: script.description,
                        category: script.category,
                        script: script.script,
                        type: currentScriptType
                    });
                    showNotification('脚本复制成功', 'success');
                    loadScripts();
                }
            } catch (error) {
                console.error('Failed to duplicate script:', error);
                showNotification('复制脚本失败', 'error');
            }
        }

        // 关闭脚本编辑器
        function closeScriptEditor() {
            document.getElementById('scriptEditor').style.display = 'none';
            currentEditingScript = null;
        }

        // ==================== 新的操作选择器功能 ====================

        // 显示操作选择器
        function showActionSelector(type) {
            actionSelectorType = type;
            currentActionType = 'script';
            document.getElementById('actionSelectorTitle').textContent =
                type === 'preRequest' ? '添加前置操作' : '添加后置操作';

            // 重置界面
            switchActionType('script');
            loadActionSelectorScripts(type === 'preRequest' ? 'PRE_REQUEST' : 'TEST');
            document.getElementById('actionSelector').style.display = 'block';
        }

        // 切换操作类型 (脚本/等待时间/HTTP请求)
        function switchActionType(type) {
            currentActionType = type;

            // 更新按钮状态
            document.getElementById('scriptActionTab').className = type === 'script' ? 'btn' : 'btn btn-secondary';
            document.getElementById('waitActionTab').className = type === 'wait' ? 'btn' : 'btn btn-secondary';
            document.getElementById('httpActionTab').className = type === 'http' ? 'btn' : 'btn btn-secondary';

            // 显示/隐藏对应面板
            document.getElementById('scriptActionPanel').style.display = type === 'script' ? 'block' : 'none';
            document.getElementById('waitActionPanel').style.display = type === 'wait' ? 'block' : 'none';
            document.getElementById('httpActionPanel').style.display = type === 'http' ? 'block' : 'none';

            // 清空输入
            if (type === 'wait') {
                document.getElementById('waitTime').value = '';
                document.getElementById('waitDescription').value = '';
            } else if (type === 'http') {
                document.getElementById('httpActionName').value = '';
                document.getElementById('httpMethod').value = 'GET';
                document.getElementById('httpUrl').value = '';
                document.getElementById('httpHeaders').value = '';
                document.getElementById('httpBody').value = '';
                document.getElementById('httpDescription').value = '';
                clearResponseMappings();
            }
        }

        // 加载操作选择器中的脚本
        async function loadActionSelectorScripts(type) {
            try {
                const scripts = await callAPI('/apiPostman/scripts/list', { type: type });
                displayActionSelectorScripts(scripts || []);
            } catch (error) {
                console.error('Failed to load action selector scripts:', error);
            }
        }

        // 显示操作选择器中的脚本
        function displayActionSelectorScripts(scripts) {
            const selectorList = document.getElementById('selectorScriptList');
            selectorList.innerHTML = '';

            if (scripts.length === 0) {
                selectorList.innerHTML = '<p style="text-align: center; color: #666;">暂无可选脚本</p>';
                return;
            }

            scripts.forEach(script => {
                const scriptCard = document.createElement('div');
                scriptCard.className = 'script-card';
                scriptCard.onclick = () => selectActionScript(script);
                scriptCard.innerHTML = `
                    <div class="script-title">${script.name}</div>
                    <div class="script-meta">
                        分类: ${script.category || '未分类'} | 使用次数: ${script.usageCount || 0}
                    </div>
                    <div class="script-preview">${(script.script || '').substring(0, 80)}${script.script && script.script.length > 80 ? '...' : ''}</div>
                `;
                selectorList.appendChild(scriptCard);
            });
        }

        // 选择操作脚本
        function selectActionScript(script) {
            // 移除之前的选中状态
            document.querySelectorAll('#selectorScriptList .script-card').forEach(card => {
                card.classList.remove('selected');
            });

            // 添加选中状态
            event.currentTarget.classList.add('selected');

            // 保存选中的脚本
            window.selectedActionScript = script;
        }

        // 确认操作选择
        function confirmActionSelection() {
            if (currentActionType === 'script') {
                if (!window.selectedActionScript) {
                    showNotification('请选择一个脚本', 'error');
                    return;
                }

                const action = {
                    type: 'script',
                    script: window.selectedActionScript,
                    id: 'script_' + Date.now(),
                    name: window.selectedActionScript.name,
                    description: window.selectedActionScript.description || '执行脚本'
                };

                addActionToList(action);
            } else if (currentActionType === 'wait') {
                const waitTime = parseInt(document.getElementById('waitTime').value);
                const description = document.getElementById('waitDescription').value.trim();

                if (!waitTime || waitTime <= 0) {
                    showNotification('请输入有效的等待时间 (大于0的毫秒数)', 'error');
                    return;
                }

                if (waitTime > 300000) {
                    showNotification('等待时间不能超过5分钟 (300000毫秒)', 'error');
                    return;
                }

                const action = {
                    type: 'wait',
                    waitTime: waitTime,
                    id: 'wait_' + Date.now(),
                    name: `等待 ${waitTime}ms`,
                    description: description || `等待 ${waitTime} 毫秒`
                };

                addActionToList(action);
            } else if (currentActionType === 'http') {
                const actionName = document.getElementById('httpActionName').value.trim();
                const httpMethod = document.getElementById('httpMethod').value;
                const httpUrl = document.getElementById('httpUrl').value.trim();
                const httpHeaders = document.getElementById('httpHeaders').value.trim();
                const httpBody = document.getElementById('httpBody').value.trim();
                const description = document.getElementById('httpDescription').value.trim();

                if (!actionName) {
                    showNotification('请输入操作名称', 'error');
                    return;
                }

                if (!httpUrl) {
                    showNotification('请输入请求URL', 'error');
                    return;
                }

                // 解析请求头
                let parsedHeaders = {};
                if (httpHeaders) {
                    try {
                        parsedHeaders = JSON.parse(httpHeaders);
                    } catch (e) {
                        showNotification('请求头格式错误，请使用有效的JSON格式', 'error');
                        return;
                    }
                }

                // 获取响应映射
                const responseMapping = responseMappingsToArray();

                const action = {
                    type: 'http',
                    httpMethod: httpMethod,
                    httpUrl: httpUrl,
                    httpHeaders: parsedHeaders,
                    httpBody: httpBody || null,
                    responseMapping: responseMapping,
                    id: 'http_' + Date.now(),
                    name: actionName,
                    description: description || `${httpMethod} ${httpUrl}`
                };

                addActionToList(action);
            }

            closeActionSelector();
        }

        // 添加操作到列表
        function addActionToList(action) {
            if (actionSelectorType === 'preRequest') {
                selectedPreRequestActions.push(action);
            } else {
                selectedPostActions.push(action);
            }
            updateSelectedActionsDisplay();
            showNotification(`已添加${action.type === 'script' ? '脚本' : '等待时间'}操作: ${action.name}`, 'success');
        }

        // 关闭操作选择器
        function closeActionSelector() {
            document.getElementById('actionSelector').style.display = 'none';
            actionSelectorType = null;
            window.selectedActionScript = null;
        }

        // 显示脚本选择器 (保留用于脚本管理页面)
        function showScriptSelector(type) {
            scriptSelectorType = type;
            document.getElementById('selectorTitle').textContent =
                type === 'preRequest' ? '选择前置操作脚本' : '选择后置操作脚本';

            loadSelectorScripts(type === 'preRequest' ? 'PRE_REQUEST' : 'TEST');
            document.getElementById('scriptSelector').style.display = 'block';
        }

        // 加载选择器中的脚本 (用于脚本管理页面)
        async function loadSelectorScripts(type) {
            try {
                const scripts = await callAPI('/apiPostman/scripts/list', { type: type });
                displaySelectorScripts(scripts || []);
            } catch (error) {
                console.error('Failed to load selector scripts:', error);
            }
        }

        // 显示选择器中的脚本 (用于脚本管理页面)
        function displaySelectorScripts(scripts) {
            const selectorList = document.getElementById('selectorScriptList2');
            selectorList.innerHTML = '';

            if (scripts.length === 0) {
                selectorList.innerHTML = '<p style="text-align: center; color: #666;">暂无可选脚本</p>';
                return;
            }

            scripts.forEach(script => {
                const isSelected = (scriptSelectorType === 'preRequest' ? selectedPreRequestScripts : selectedTestScripts)
                    .some(s => s.id === script.id);

                const scriptCard = document.createElement('div');
                scriptCard.className = `script-card ${isSelected ? 'selected' : ''}`;
                scriptCard.onclick = () => toggleScriptSelection(script);
                scriptCard.innerHTML = `
                    <div class="script-title">${script.name}</div>
                    <div class="script-meta">
                        分类: ${script.category || '未分类'} | 使用次数: ${script.usageCount || 0}
                    </div>
                    <div class="script-preview">${(script.script || '').substring(0, 80)}${script.script && script.script.length > 80 ? '...' : ''}</div>
                `;
                selectorList.appendChild(scriptCard);
            });
        }

        // 切换脚本选择状态
        function toggleScriptSelection(script) {
            const targetArray = scriptSelectorType === 'preRequest' ? selectedPreRequestScripts : selectedTestScripts;
            const index = targetArray.findIndex(s => s.id === script.id);

            if (index >= 0) {
                targetArray.splice(index, 1);
            } else {
                targetArray.push(script);
            }

            // 重新加载选择器显示
            loadSelectorScripts(scriptSelectorType === 'preRequest' ? 'PRE_REQUEST' : 'TEST');
        }

        // 确认脚本选择
        function confirmScriptSelection() {
            updateSelectedScriptsDisplay();
            closeScriptSelector();
        }

        // 关闭脚本选择器
        function closeScriptSelector() {
            document.getElementById('scriptSelector').style.display = 'none';
            scriptSelectorType = null;
        }

        // 更新已选择操作的显示
        function updateSelectedActionsDisplay() {
            updateActionDisplay('preRequest', selectedPreRequestActions, 'preActionCount', 'preActionList');
            updateActionDisplay('post', selectedPostActions, 'postActionCount', 'postActionList');

            // 保持向后兼容性
            updateSelectedScriptsDisplay();
        }

        // 更新单个操作类型的显示
        function updateActionDisplay(type, actions, countElementId, listElementId) {
            const countElement = document.getElementById(countElementId);
            const listElement = document.getElementById(listElementId);

            if (countElement) {
                countElement.textContent = actions.length;
            }

            if (listElement) {
                listElement.innerHTML = '';

                actions.forEach((action, index) => {
                    const actionItem = document.createElement('div');
                    actionItem.className = 'script-item';

                    let actionIcon = '';
                    let actionDetails = '';

                    if (action.type === 'script') {
                        actionIcon = '📜';
                        actionDetails = `脚本: ${action.name}`;
                    } else if (action.type === 'wait') {
                        actionIcon = '⏱️';
                        actionDetails = `等待: ${action.waitTime}ms`;
                    } else if (action.type === 'http') {
                        actionIcon = '🌐';
                        actionDetails = `HTTP: ${action.httpMethod} ${action.name}`;
                    }

                    actionItem.innerHTML = `
                        <div>
                            <div class="script-name">${actionIcon} ${actionDetails}</div>
                            <div class="script-description">${action.description || '无描述'}</div>
                        </div>
                        <div>
                            <button class="btn btn-small" onclick="moveAction('${type}', ${index}, 'up')" ${index === 0 ? 'disabled' : ''}>↑</button>
                            <button class="btn btn-small" onclick="moveAction('${type}', ${index}, 'down')" ${index === actions.length - 1 ? 'disabled' : ''}>↓</button>
                            <button class="btn btn-small btn-secondary" onclick="removeSelectedAction('${type}', ${index})">移除</button>
                        </div>
                    `;
                    listElement.appendChild(actionItem);
                });
            }
        }

        // 移动操作顺序
        function moveAction(type, index, direction) {
            const actions = type === 'preRequest' ? selectedPreRequestActions : selectedPostActions;

            if (direction === 'up' && index > 0) {
                [actions[index], actions[index - 1]] = [actions[index - 1], actions[index]];
            } else if (direction === 'down' && index < actions.length - 1) {
                [actions[index], actions[index + 1]] = [actions[index + 1], actions[index]];
            }

            updateSelectedActionsDisplay();
        }

        // 移除已选择的操作
        function removeSelectedAction(type, index) {
            if (type === 'preRequest') {
                selectedPreRequestActions.splice(index, 1);
            } else {
                selectedPostActions.splice(index, 1);
            }
            updateSelectedActionsDisplay();
        }

        // 更新已选择脚本的显示 (保持向后兼容性)
        function updateSelectedScriptsDisplay() {
            updateScriptDisplay('preRequest', selectedPreRequestScripts, 'preScriptCount', 'preScriptList');
            updateScriptDisplay('test', selectedTestScripts, 'testScriptCount', 'testScriptList');
        }

        // 更新单个脚本类型的显示
        function updateScriptDisplay(type, scripts, countElementId, listElementId) {
            document.getElementById(countElementId).textContent = scripts.length;

            const listElement = document.getElementById(listElementId);
            listElement.innerHTML = '';

            scripts.forEach(script => {
                const scriptItem = document.createElement('div');
                scriptItem.className = 'script-item';
                scriptItem.innerHTML = `
                    <div>
                        <div class="script-name">${script.name}</div>
                        <div class="script-description">${script.description || '无描述'}</div>
                    </div>
                    <button class="btn btn-small btn-secondary" onclick="removeSelectedScript('${type}', '${script.id}')">移除</button>
                `;
                listElement.appendChild(scriptItem);
            });
        }

        // 移除已选择的脚本
        function removeSelectedScript(type, scriptId) {
            if (type === 'preRequest') {
                selectedPreRequestScripts = selectedPreRequestScripts.filter(s => s.id !== scriptId);
            } else {
                selectedTestScripts = selectedTestScripts.filter(s => s.id !== scriptId);
            }
            updateSelectedScriptsDisplay();
        }

        // 切换脚本编辑器显示
        function toggleScriptEditor(textareaId) {
            const textarea = document.getElementById(textareaId);
            if (textarea.style.display === 'none') {
                textarea.style.display = 'block';
            } else {
                textarea.style.display = 'none';
            }
        }

        // ==================== 参数管理功能 ====================

        // 添加Path参数
        function addPathParam(name = '', value = '') {
            const paramId = 'path_' + Date.now();
            pathParams.push({ id: paramId, name: name, value: value });
            renderPathParams();
        }

        // 添加Query参数
        function addQueryParam(name = '', value = '') {
            const paramId = 'query_' + Date.now();
            queryParams.push({ id: paramId, name: name, value: value });
            renderQueryParams();
        }

        // 渲染Path参数列表
        function renderPathParams() {
            const container = document.getElementById('pathParamsList');
            container.innerHTML = '';

            pathParams.forEach(param => {
                const row = document.createElement('div');
                row.className = 'param-row';
                row.innerHTML = `
                    <input type="text" placeholder="参数名 (如: userId)" value="${param.name}"
                           onchange="updatePathParam('${param.id}', 'name', this.value)">
                    <input type="text" placeholder="参数值 (如: {{userId}} 或 123)" value="${param.value}"
                           onchange="updatePathParam('${param.id}', 'value', this.value)">
                    <div class="param-actions">
                        <button class="btn-remove" onclick="removePathParam('${param.id}')">删除</button>
                    </div>
                `;
                container.appendChild(row);
            });
        }

        // 渲染Query参数列表
        function renderQueryParams() {
            const container = document.getElementById('queryParamsList');
            container.innerHTML = '';

            queryParams.forEach(param => {
                const row = document.createElement('div');
                row.className = 'param-row';
                row.innerHTML = `
                    <input type="text" placeholder="参数名 (如: status)" value="${param.name}"
                           onchange="updateQueryParam('${param.id}', 'name', this.value)">
                    <input type="text" placeholder="参数值 (如: {{status}} 或 active)" value="${param.value}"
                           onchange="updateQueryParam('${param.id}', 'value', this.value)">
                    <div class="param-actions">
                        <button class="btn-remove" onclick="removeQueryParam('${param.id}')">删除</button>
                    </div>
                `;
                container.appendChild(row);
            });
        }

        // 更新Path参数
        function updatePathParam(paramId, field, value) {
            const param = pathParams.find(p => p.id === paramId);
            if (param) {
                param[field] = value;
            }
        }

        // 更新Query参数
        function updateQueryParam(paramId, field, value) {
            const param = queryParams.find(p => p.id === paramId);
            if (param) {
                param[field] = value;
            }
        }

        // 删除Path参数
        function removePathParam(paramId) {
            pathParams = pathParams.filter(p => p.id !== paramId);
            renderPathParams();
        }

        // 删除Query参数
        function removeQueryParam(paramId) {
            queryParams = queryParams.filter(p => p.id !== paramId);
            renderQueryParams();
        }

        // 清空Path参数
        function clearPathParams() {
            pathParams = [];
            renderPathParams();
        }

        // 清空Query参数
        function clearQueryParams() {
            queryParams = [];
            renderQueryParams();
        }

        // 构建最终的URL（包含Path参数替换和Query参数拼接）
        function buildFinalUrl(baseUrl, pathParamsObj, queryParamsObj) {
            let finalUrl = baseUrl;

            // 替换Path参数
            Object.keys(pathParamsObj).forEach(key => {
                const placeholder = `{${key}}`;
                if (finalUrl.includes(placeholder)) {
                    finalUrl = finalUrl.replace(new RegExp(`\\{${key}\\}`, 'g'), pathParamsObj[key]);
                }
            });

            // 添加Query参数
            const queryString = Object.keys(queryParamsObj)
                .filter(key => queryParamsObj[key] !== '')
                .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(queryParamsObj[key])}`)
                .join('&');

            if (queryString) {
                finalUrl += (finalUrl.includes('?') ? '&' : '?') + queryString;
            }

            return finalUrl;
        }

        // 将参数数组转换为对象
        function paramsArrayToObject(paramsArray) {
            const obj = {};
            paramsArray.forEach(param => {
                if (param.name && param.name.trim()) {
                    obj[param.name.trim()] = param.value || '';
                }
            });
            return obj;
        }

        // ==================== HTTP请求保存和管理功能 ====================

        // 保存当前HTTP请求
        function saveCurrentHttpRequest() {
            // 检查是否有基本的请求信息
            const url = document.getElementById('url').value.trim();
            const method = document.getElementById('method').value;

            if (!url) {
                showNotification('请先输入请求URL', 'error');
                return;
            }

            // 显示保存对话框
            document.getElementById('saveHttpRequestDialog').style.display = 'block';

            // 自动生成请求名称
            const autoName = `${method} ${url}`;
            document.getElementById('httpRequestName').value = autoName;
        }

        // 关闭保存HTTP请求对话框
        function closeSaveHttpRequestDialog() {
            document.getElementById('saveHttpRequestDialog').style.display = 'none';
            // 清空表单
            document.getElementById('httpRequestName').value = '';
            document.getElementById('httpRequestCategory').value = 'API';
            document.getElementById('httpRequestDescription').value = '';
        }

        // 确认保存HTTP请求
        async function confirmSaveHttpRequest() {
            const name = document.getElementById('httpRequestName').value.trim();
            const category = document.getElementById('httpRequestCategory').value;
            const description = document.getElementById('httpRequestDescription').value.trim();

            if (!name) {
                showNotification('请输入请求名称', 'error');
                return;
            }

            try {
                // 解析请求头
                let headers = {};
                const headersText = document.getElementById('headers').value.trim();
                if (headersText) {
                    try {
                        headers = JSON.parse(headersText);
                    } catch (e) {
                        headers = {};
                    }
                }

                // 构建HttpRequestPM对象
                const httpRequestPM = {
                    method: document.getElementById('method').value,
                    url: document.getElementById('url').value.trim(),
                    headers: headers,
                    body: document.getElementById('body').value.trim(),
                    pathParams: pathParamsToObject(),
                    queryParams: queryParamsToObject(),
                    preRequestActions: Array.isArray(selectedPreRequestActions) ? selectedPreRequestActions : [],
                    postRequestActions: Array.isArray(selectedPostActions) ? selectedPostActions : [],
                    environmentId: currentEnvironmentId
                };

                // 构建符合后端期望的参数结构
                const requestData = {
                    name: name,
                    description: description,
                    category: category,
                    request: httpRequestPM
                };

                console.log('保存HTTP请求，发送数据:', requestData);

                // 调用保存API
                const result = await callAPI('/apiPostman/requests/save', requestData);

                showNotification('HTTP请求保存成功', 'success');
                closeSaveHttpRequestDialog();

            } catch (error) {
                console.error('Failed to save HTTP request:', error);
                showNotification('保存HTTP请求失败: ' + (error.message || '未知错误'), 'error');
            }
        }

        // 显示保存的HTTP请求
        function showSavedHttpRequests() {
            document.getElementById('savedHttpRequestsDialog').style.display = 'block';
            loadSavedHttpRequests();
        }

        // 关闭保存的HTTP请求对话框
        function closeSavedHttpRequestsDialog() {
            document.getElementById('savedHttpRequestsDialog').style.display = 'none';
        }

        // 加载保存的HTTP请求列表
        async function loadSavedHttpRequests() {
            try {
                const requests = await callAPI('/apiPostman/requests/list', {});
                displaySavedHttpRequests(requests || []);
            } catch (error) {
                console.error('Failed to load saved HTTP requests:', error);
                showNotification('加载保存的请求失败', 'error');
            }
        }

        // 显示保存的HTTP请求列表
        function displaySavedHttpRequests(requests) {
            const container = document.getElementById('savedHttpRequestsList');
            container.innerHTML = '';

            if (requests.length === 0) {
                container.innerHTML = '<p style="text-align: center; color: #666;">暂无保存的HTTP请求</p>';
                return;
            }

            requests.forEach(request => {
                const requestCard = document.createElement('div');
                requestCard.className = 'script-card';
                requestCard.innerHTML = `
                    <div class="script-header">
                        <h4>${request.name}</h4>
                        <span class="script-category">${request.category || 'API'}</span>
                    </div>
                    <div class="script-info">
                        <p><strong>${request.method}</strong> ${request.url}</p>
                        <p style="color: #666; font-size: 14px;">${request.description || '无描述'}</p>
                        <p style="color: #999; font-size: 12px;">创建时间: ${new Date(request.createdAt).toLocaleString()}</p>
                    </div>
                    <div class="script-actions">
                        <button class="btn btn-small" onclick="loadSavedHttpRequest('${request.id}')">加载</button>
                        <button class="btn btn-small" style="background: #28a745; border-color: #28a745;" onclick="executeHttpRequest('${request.id}')">执行</button>
                        <button class="btn btn-small btn-secondary" onclick="editSavedHttpRequest('${request.id}')">编辑</button>
                        <button class="btn btn-small btn-secondary" onclick="duplicateSavedHttpRequest('${request.id}')">复制</button>
                        <button class="btn btn-small btn-secondary" onclick="deleteSavedHttpRequest('${request.id}')">删除</button>
                    </div>
                `;
                container.appendChild(requestCard);
            });
        }

        // 搜索保存的HTTP请求
        async function searchSavedHttpRequests() {
            const searchQuery = document.getElementById('savedRequestSearch').value.trim();
            const categoryFilter = document.getElementById('savedRequestCategoryFilter').value;

            try {
                const params = {};
                if (searchQuery) params.name = searchQuery;
                if (categoryFilter) params.category = categoryFilter;

                const requests = await callAPI('/apiPostman/requests/search', params);
                displaySavedHttpRequests(requests || []);
            } catch (error) {
                console.error('Failed to search saved HTTP requests:', error);
                showNotification('搜索保存的请求失败', 'error');
            }
        }

        // 加载保存的HTTP请求到表单
        async function loadSavedHttpRequest(requestId) {
            try {
                const request = await callAPI('/apiPostman/requests/get', { id: requestId });
                if (request) {
                    // 加载到表单
                    document.getElementById('method').value = request.method || 'GET';
                    document.getElementById('url').value = request.url || '';
                    document.getElementById('headers').value = JSON.stringify(request.headers || {}, null, 2);
                    document.getElementById('body').value = request.body || '';

                    // 加载参数
                    if (request.pathParams) {
                        pathParams = Object.entries(request.pathParams).map(([key, value]) => ({
                            id: 'param_' + Date.now() + '_' + Math.random(),
                            key: key,
                            value: value
                        }));
                        renderPathParams();
                    }

                    if (request.queryParams) {
                        queryParams = Object.entries(request.queryParams).map(([key, value]) => ({
                            id: 'query_' + Date.now() + '_' + Math.random(),
                            key: key,
                            value: value
                        }));
                        renderQueryParams();
                    }

                    // 加载操作
                    if (request.preRequestActions) {
                        selectedPreRequestActions = request.preRequestActions;
                        updateSelectedActionsDisplay();
                    }

                    if (request.postRequestActions) {
                        selectedPostActions = request.postRequestActions;
                        updateSelectedActionsDisplay();
                    }

                    showNotification('HTTP请求加载成功', 'success');
                    closeSavedHttpRequestsDialog();

                    // 切换到HTTP请求标签页
                    showTab('request');
                }
            } catch (error) {
                console.error('Failed to load saved HTTP request:', error);
                showNotification('加载HTTP请求失败', 'error');
            }
        }

        // 编辑保存的HTTP请求
        async function editSavedHttpRequest(requestId) {
            // 先加载到表单
            await loadSavedHttpRequest(requestId);
            // 然后显示保存对话框进行编辑
            saveCurrentHttpRequest();
        }

        // 复制保存的HTTP请求
        async function duplicateSavedHttpRequest(requestId) {
            try {
                const result = await callAPI('/apiPostman/requests/duplicate', { id: requestId });
                showNotification('HTTP请求复制成功', 'success');
                loadSavedHttpRequests(); // 刷新列表
            } catch (error) {
                console.error('Failed to duplicate saved HTTP request:', error);
                showNotification('复制HTTP请求失败', 'error');
            }
        }

        // 删除保存的HTTP请求
        async function deleteSavedHttpRequest(requestId) {
            if (!confirm('确定要删除这个保存的HTTP请求吗？')) {
                return;
            }

            try {
                await callAPI('/apiPostman/requests/delete', { id: requestId });
                showNotification('HTTP请求删除成功', 'success');
                loadSavedHttpRequests(); // 刷新列表
            } catch (error) {
                console.error('Failed to delete saved HTTP request:', error);
                showNotification('删除HTTP请求失败', 'error');
            }
        }

        // 执行保存的HTTP请求
        async function executeHttpRequest(requestId) {
            try {
                // 获取保存的请求详情
                const request = await callAPI('/apiPostman/requests/get', { id: requestId });
                if (!request) {
                    showNotification('获取请求详情失败', 'error');
                    return;
                }

                // 设置当前执行的请求ID
                currentExecuteRequestId = requestId;

                // 显示请求信息
                document.getElementById('executeRequestMethod').textContent = request.method || 'GET';
                document.getElementById('executeRequestUrl').textContent = request.url || '';
                document.getElementById('executeRequestDescription').textContent = request.description || '无描述';
                document.getElementById('executeRequestTitle').textContent = `执行请求: ${request.name}`;

                // 预填充现有参数
                if (request.pathParams) {
                    executePathParams = Object.entries(request.pathParams).map(([key, value]) => ({
                        id: 'exec_path_' + Date.now() + '_' + Math.random(),
                        key: key,
                        value: value
                    }));
                } else {
                    executePathParams = [];
                }

                if (request.queryParams) {
                    executeQueryParams = Object.entries(request.queryParams).map(([key, value]) => ({
                        id: 'exec_query_' + Date.now() + '_' + Math.random(),
                        key: key,
                        value: value
                    }));
                } else {
                    executeQueryParams = [];
                }

                // 预填充请求头和请求体
                document.getElementById('executeHeaders').value = JSON.stringify(request.headers || {}, null, 2);
                document.getElementById('executeBody').value = request.body || '';

                // 渲染参数列表
                renderExecutePathParams();
                renderExecuteQueryParams();

                // 显示执行对话框
                document.getElementById('executeHttpRequestDialog').style.display = 'block';

            } catch (error) {
                console.error('Failed to prepare HTTP request execution:', error);
                showNotification('准备执行请求失败', 'error');
            }
        }

        // 关闭执行HTTP请求对话框
        function closeExecuteHttpRequestDialog() {
            document.getElementById('executeHttpRequestDialog').style.display = 'none';
            // 清空数据
            currentExecuteRequestId = null;
            executePathParams = [];
            executeQueryParams = [];
            document.getElementById('executeHeaders').value = '';
            document.getElementById('executeBody').value = '';
        }

        // 确认执行HTTP请求
        async function confirmExecuteHttpRequest() {
            if (!currentExecuteRequestId) {
                showNotification('请求ID丢失', 'error');
                return;
            }

            try {
                // 获取原始请求
                const originalRequest = await callAPI('/apiPostman/requests/get', { id: currentExecuteRequestId });
                if (!originalRequest) {
                    showNotification('获取原始请求失败', 'error');
                    return;
                }

                // 解析执行参数
                let executeHeaders = {};
                const headersText = document.getElementById('executeHeaders').value.trim();
                if (headersText) {
                    try {
                        executeHeaders = JSON.parse(headersText);
                    } catch (e) {
                        showNotification('请求头格式错误，请使用有效的JSON格式', 'error');
                        return;
                    }
                }

                // 构建执行请求
                const executeRequestData = {
                    method: originalRequest.method,
                    url: originalRequest.url,
                    headers: executeHeaders,
                    body: document.getElementById('executeBody').value.trim(),
                    pathParams: executePathParamsToObject(),
                    queryParams: executeQueryParamsToObject(),
                    preRequestActions: originalRequest.preRequestActions || [],
                    postRequestActions: originalRequest.postRequestActions || [],
                    environmentId: currentEnvironmentId || originalRequest.environmentId
                };

                console.log('执行HTTP请求，数据:', executeRequestData);

                // 关闭执行对话框
                closeExecuteHttpRequestDialog();

                // 切换到HTTP请求标签页
                showTab('request');

                // 执行请求
                const response = await callAPI('/apiPostman/requests/execute', executeRequestData);

                // 显示响应结果
                displayResponse(response);
                showNotification('HTTP请求执行成功', 'success');

            } catch (error) {
                console.error('Failed to execute HTTP request:', error);
                showNotification('执行HTTP请求失败: ' + (error.message || '未知错误'), 'error');
            }
        }

        // ==================== 执行参数管理功能 ====================

        // 添加执行Path参数
        function addExecutePathParam(key = '', value = '') {
            const paramId = 'exec_path_' + Date.now() + '_' + Math.random();
            executePathParams.push({ id: paramId, key: key, value: value });
            renderExecutePathParams();
        }

        // 渲染执行Path参数列表
        function renderExecutePathParams() {
            const container = document.getElementById('executePathParamsList');
            container.innerHTML = '';

            executePathParams.forEach(param => {
                const row = document.createElement('div');
                row.className = 'param-row';
                row.innerHTML = `
                    <input type="text" placeholder="参数名" value="${param.key}"
                           onchange="updateExecutePathParam('${param.id}', 'key', this.value)"
                           style="width: 40%;">
                    <input type="text" placeholder="参数值" value="${param.value}"
                           onchange="updateExecutePathParam('${param.id}', 'value', this.value)"
                           style="width: 40%;">
                    <div class="param-actions" style="width: 20%;">
                        <button class="btn-remove" onclick="removeExecutePathParam('${param.id}')">删除</button>
                    </div>
                `;
                container.appendChild(row);
            });
        }

        // 更新执行Path参数
        function updateExecutePathParam(paramId, field, value) {
            const param = executePathParams.find(p => p.id === paramId);
            if (param) {
                param[field] = value;
            }
        }

        // 删除执行Path参数
        function removeExecutePathParam(paramId) {
            executePathParams = executePathParams.filter(p => p.id !== paramId);
            renderExecutePathParams();
        }

        // 清空执行Path参数
        function clearExecutePathParams() {
            executePathParams = [];
            renderExecutePathParams();
        }

        // 添加执行Query参数
        function addExecuteQueryParam(key = '', value = '') {
            const paramId = 'exec_query_' + Date.now() + '_' + Math.random();
            executeQueryParams.push({ id: paramId, key: key, value: value });
            renderExecuteQueryParams();
        }

        // 渲染执行Query参数列表
        function renderExecuteQueryParams() {
            const container = document.getElementById('executeQueryParamsList');
            container.innerHTML = '';

            executeQueryParams.forEach(param => {
                const row = document.createElement('div');
                row.className = 'param-row';
                row.innerHTML = `
                    <input type="text" placeholder="参数名" value="${param.key}"
                           onchange="updateExecuteQueryParam('${param.id}', 'key', this.value)"
                           style="width: 40%;">
                    <input type="text" placeholder="参数值" value="${param.value}"
                           onchange="updateExecuteQueryParam('${param.id}', 'value', this.value)"
                           style="width: 40%;">
                    <div class="param-actions" style="width: 20%;">
                        <button class="btn-remove" onclick="removeExecuteQueryParam('${param.id}')">删除</button>
                    </div>
                `;
                container.appendChild(row);
            });
        }

        // 更新执行Query参数
        function updateExecuteQueryParam(paramId, field, value) {
            const param = executeQueryParams.find(p => p.id === paramId);
            if (param) {
                param[field] = value;
            }
        }

        // 删除执行Query参数
        function removeExecuteQueryParam(paramId) {
            executeQueryParams = executeQueryParams.filter(p => p.id !== paramId);
            renderExecuteQueryParams();
        }

        // 清空执行Query参数
        function clearExecuteQueryParams() {
            executeQueryParams = [];
            renderExecuteQueryParams();
        }

        // 将执行Path参数数组转换为对象
        function executePathParamsToObject() {
            const result = {};
            executePathParams.forEach(param => {
                if (param.key && param.key.trim()) {
                    result[param.key] = param.value || '';
                }
            });
            return result;
        }

        // 将执行Query参数数组转换为对象
        function executeQueryParamsToObject() {
            const result = {};
            executeQueryParams.forEach(param => {
                if (param.key && param.key.trim()) {
                    result[param.key] = param.value || '';
                }
            });
            return result;
        }

        // ==================== 响应映射管理功能 ====================

        // 添加响应映射
        function addResponseMapping(jsonPath = '', variableName = '', required = false) {
            const mappingId = 'mapping_' + Date.now();
            responseMappings.push({
                id: mappingId,
                jsonPath: jsonPath,
                variableName: variableName,
                required: required
            });
            renderResponseMappings();
        }

        // 渲染响应映射列表
        function renderResponseMappings() {
            const container = document.getElementById('responseMappingList');
            container.innerHTML = '';

            responseMappings.forEach(mapping => {
                const row = document.createElement('div');
                row.className = 'param-row';
                row.innerHTML = `
                    <input type="text" placeholder="$.data.token" value="${mapping.jsonPath}"
                           onchange="updateResponseMapping('${mapping.id}', 'jsonPath', this.value)"
                           style="width: 35%;">
                    <input type="text" placeholder="authToken" value="${mapping.variableName}"
                           onchange="updateResponseMapping('${mapping.id}', 'variableName', this.value)"
                           style="width: 25%;">
                    <label style="width: 20%; text-align: center;">
                        <input type="checkbox" ${mapping.required ? 'checked' : ''}
                               onchange="updateResponseMapping('${mapping.id}', 'required', this.checked)">
                        必需
                    </label>
                    <div class="param-actions" style="width: 20%;">
                        <button class="btn-remove" onclick="removeResponseMapping('${mapping.id}')">删除</button>
                    </div>
                `;
                container.appendChild(row);
            });
        }

        // 更新响应映射
        function updateResponseMapping(mappingId, field, value) {
            const mapping = responseMappings.find(m => m.id === mappingId);
            if (mapping) {
                mapping[field] = value;
            }
        }

        // 删除响应映射
        function removeResponseMapping(mappingId) {
            responseMappings = responseMappings.filter(m => m.id !== mappingId);
            renderResponseMappings();
        }

        // 清空响应映射
        function clearResponseMappings() {
            responseMappings = [];
            renderResponseMappings();
        }

        // 将响应映射数组转换为对象数组
        function responseMappingsToArray() {
            return responseMappings.map(mapping => ({
                jsonPath: mapping.jsonPath,
                variableName: mapping.variableName,
                required: mapping.required || false,
                description: `${mapping.jsonPath} -> ${mapping.variableName}`
            })).filter(mapping => mapping.jsonPath && mapping.variableName);
        }

        // 将路径参数数组转换为对象
        function pathParamsToObject() {
            const result = {};
            pathParams.forEach(param => {
                if (param.key && param.key.trim()) {
                    result[param.key] = param.value || '';
                }
            });
            return result;
        }

        // 将查询参数数组转换为对象
        function queryParamsToObject() {
            const result = {};
            queryParams.forEach(param => {
                if (param.key && param.key.trim()) {
                    result[param.key] = param.value || '';
                }
            });
            return result;
        }

        // ==================== 请求历史功能 ====================

        // 加载历史请求列表
        async function loadHttpRequests() {
            try {
                const result = await callAPI('/apiPostman/requests/list', {});
                allHttpRequests = result || [];
                displayHttpRequests(allHttpRequests);
                updateHttpRequestStats();
            } catch (error) {
                console.error('Failed to load HTTP requests:', error);
                showNotification('加载历史请求失败', 'error');
            }
        }

        // 显示历史请求列表
        function displayHttpRequests(requests) {
            const requestList = document.getElementById('requestList');
            requestList.innerHTML = '';

            if (requests.length === 0) {
                requestList.innerHTML = '<p style="text-align: center; color: #666;">暂无历史请求记录</p>';
                return;
            }

            requests.forEach(request => {
                const requestCard = document.createElement('div');
                requestCard.className = 'script-card';

                const statusIcon = getStatusIcon(request.lastResponseStatus);
                const categoryBadge = request.category ? `<span style="background: #007bff; color: white; padding: 2px 6px; border-radius: 3px; font-size: 10px;">${request.category}</span>` : '';

                requestCard.innerHTML = `
                    <div class="script-title">${request.name} ${categoryBadge}</div>
                    <div class="script-meta">
                        ${statusIcon} ${request.method} |
                        使用次数: ${request.usageCount || 0} |
                        版本: ${request.version || 1}
                        ${request.lastExecutedAt ? `<br>最后执行: ${new Date(request.lastExecutedAt).toLocaleString()}` : ''}
                    </div>
                    <div class="script-preview">${request.url.substring(0, 80)}${request.url.length > 80 ? '...' : ''}</div>
                    <div style="margin-top: 10px;">
                        <button class="btn btn-small" onclick="viewRequestDetail('${request.id}')">查看</button>
                        <button class="btn btn-small" onclick="loadRequestToCurrentForm('${request.id}')">加载</button>
                        <button class="btn btn-small" onclick="editHttpRequest('${request.id}')">编辑</button>
                        <button class="btn btn-small btn-secondary" onclick="deleteHttpRequest('${request.id}')">删除</button>
                        <button class="btn btn-small" onclick="duplicateHttpRequest('${request.id}')">复制</button>
                    </div>
                `;
                requestList.appendChild(requestCard);
            });
        }

        // 获取状态图标
        function getStatusIcon(statusCode) {
            if (!statusCode) return '⚪';
            if (statusCode >= 200 && statusCode < 300) return '🟢';
            if (statusCode >= 300 && statusCode < 400) return '🟡';
            if (statusCode >= 400 && statusCode < 500) return '🔴';
            if (statusCode >= 500) return '⚫';
            return '⚪';
        }

        // 更新历史请求统计信息
        async function updateHttpRequestStats() {
            try {
                const stats = await callAPI('/apiPostman/requests/stats', {});
                const statsDiv = document.getElementById('requestStats');
                statsDiv.innerHTML = `
                    历史记录: ${stats.totalCount} 个请求 |
                    有效记录: ${stats.activeCount} 个 |
                    分类统计: ${Object.entries(stats.categoryStats || {}).map(([k, v]) => `${k}(${v})`).join(', ')}
                `;
            } catch (error) {
                console.error('Failed to load request stats:', error);
            }
        }

        // 显示保存当前请求对话框
        function showSaveCurrentRequestDialog() {
            // 自动填充当前请求信息
            const method = document.getElementById('method').value;
            const url = document.getElementById('url').value;

            if (!url.trim()) {
                showNotification('请先输入请求URL', 'error');
                return;
            }

            // 生成默认名称
            const defaultName = `${method} ${extractUrlPath(url)}`;
            document.getElementById('saveRequestName').value = defaultName;
            document.getElementById('saveRequestDescription').value = '';
            document.getElementById('saveRequestCategory').value = '';
            document.getElementById('saveRequestTags').value = '';

            document.getElementById('saveRequestDialog').style.display = 'block';
        }

        // 提取URL路径用于生成名称
        function extractUrlPath(url) {
            try {
                const urlObj = new URL(url);
                const path = urlObj.pathname;
                if (path && path !== '/') {
                    const pathParts = path.split('/').filter(p => p);
                    return pathParts.length > 0 ? pathParts[pathParts.length - 1] : urlObj.host;
                }
                return urlObj.host;
            } catch (e) {
                return url.substring(0, 30);
            }
        }

        // 保存当前请求
        async function saveCurrentRequest() {
            const name = document.getElementById('saveRequestName').value.trim();
            const description = document.getElementById('saveRequestDescription').value.trim();
            const category = document.getElementById('saveRequestCategory').value;
            const tagsStr = document.getElementById('saveRequestTags').value.trim();

            if (!name) {
                showNotification('请输入请求名称', 'error');
                return;
            }

            // 构建当前请求对象
            const pathParamsObj = paramsArrayToObject(pathParams);
            const queryParamsObj = paramsArrayToObject(queryParams);
            const baseUrl = document.getElementById('url').value;

            const currentRequest = {
                method: document.getElementById('method').value,
                url: baseUrl,
                originalUrl: baseUrl,
                pathParams: pathParamsObj,
                queryParams: queryParamsObj,
                headers: JSON.parse(document.getElementById('headers').value || '{}'),
                body: document.getElementById('body').value,
                preRequestScript: document.getElementById('preScript').value,
                testScript: document.getElementById('testScript').value,
                preRequestScriptIds: selectedPreRequestActions
                    .filter(action => action.type === 'script')
                    .map(action => action.script.id),
                testScriptIds: selectedPostActions
                    .filter(action => action.type === 'script')
                    .map(action => action.script.id),
                preRequestActions: selectedPreRequestActions,
                postActions: selectedPostActions,
                environmentId: currentEnvironmentId
            };

            const tags = tagsStr ? tagsStr.split(',').map(t => t.trim()).filter(t => t) : [];

            try {
                await callAPI('/apiPostman/requests/save', {
                    name: name,
                    description: description,
                    request: currentRequest,
                    category: category,
                    tags: tags
                });

                closeSaveRequestDialog();
                loadHttpRequests();
                showNotification('请求保存成功', 'success');
            } catch (error) {
                console.error('Failed to save request:', error);
            }
        }

        // 关闭保存请求对话框
        function closeSaveRequestDialog() {
            document.getElementById('saveRequestDialog').style.display = 'none';
        }

        // 搜索历史请求
        async function searchHttpRequests() {
            const searchQuery = document.getElementById('requestSearch').value.trim();
            const categoryFilter = document.getElementById('requestCategoryFilter').value;

            try {
                let requests = allHttpRequests;

                if (searchQuery) {
                    const searchResult = await callAPI('/apiPostman/requests/search', {
                        searchQuery: searchQuery
                    });
                    requests = searchResult || [];
                }

                if (categoryFilter) {
                    const categoryResult = await callAPI('/apiPostman/requests/list', {
                        category: categoryFilter
                    });
                    requests = categoryResult || [];
                }

                if (searchQuery && categoryFilter) {
                    requests = requests.filter(request =>
                        request.name.toLowerCase().includes(searchQuery.toLowerCase()) &&
                        request.category === categoryFilter
                    );
                }

                displayHttpRequests(requests);
            } catch (error) {
                console.error('Failed to search requests:', error);
                showNotification('搜索历史请求失败', 'error');
            }
        }

        // 查看请求详情
        async function viewRequestDetail(requestId) {
            try {
                const request = await callAPI('/apiPostman/requests/get', { id: requestId });
                if (request) {
                    currentViewingRequest = request;
                    displayRequestDetail(request);
                    document.getElementById('requestDetailDialog').style.display = 'block';
                }
            } catch (error) {
                console.error('Failed to get request detail:', error);
                showNotification('获取请求详情失败', 'error');
            }
        }

        // 显示请求详情
        function displayRequestDetail(request) {
            const content = document.getElementById('requestDetailContent');
            content.innerHTML = `
                <div class="form-group">
                    <label>请求名称:</label>
                    <div style="padding: 8px; background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 4px;">
                        ${request.name}
                    </div>
                </div>
                <div class="form-group">
                    <label>请求描述:</label>
                    <div style="padding: 8px; background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 4px;">
                        ${request.description || '无描述'}
                    </div>
                </div>
                <div class="form-group">
                    <label>请求方法和URL:</label>
                    <div style="padding: 8px; background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 4px;">
                        <strong>${request.method}</strong> ${request.url}
                        ${request.originalUrl && request.originalUrl !== request.url ? `<br><small style="color: #666;">原始URL: ${request.originalUrl}</small>` : ''}
                    </div>
                </div>
                ${request.pathParams && Object.keys(request.pathParams).length > 0 ? `
                <div class="form-group">
                    <label>Path参数:</label>
                    <div style="padding: 8px; background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 4px;">
                        ${Object.keys(request.pathParams).map(key => `<div><strong>${key}:</strong> ${request.pathParams[key]}</div>`).join('')}
                    </div>
                </div>
                ` : ''}
                ${request.queryParams && Object.keys(request.queryParams).length > 0 ? `
                <div class="form-group">
                    <label>Query参数:</label>
                    <div style="padding: 8px; background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 4px;">
                        ${Object.keys(request.queryParams).map(key => `<div><strong>${key}:</strong> ${request.queryParams[key]}</div>`).join('')}
                    </div>
                </div>
                ` : ''}
                <div class="form-group">
                    <label>请求头:</label>
                    <pre style="background: #f8f9fa; padding: 8px; border: 1px solid #dee2e6; border-radius: 4px; max-height: 150px; overflow-y: auto;">${JSON.stringify(request.headers, null, 2)}</pre>
                </div>
                ${request.body ? `
                <div class="form-group">
                    <label>请求体:</label>
                    <pre style="background: #f8f9fa; padding: 8px; border: 1px solid #dee2e6; border-radius: 4px; max-height: 150px; overflow-y: auto;">${request.body}</pre>
                </div>
                ` : ''}
                <div class="form-group">
                    <label>统计信息:</label>
                    <div style="padding: 8px; background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 4px;">
                        分类: ${request.category || '未分类'} |
                        使用次数: ${request.usageCount || 0} |
                        版本: ${request.version || 1}<br>
                        创建时间: ${request.createdAt ? new Date(request.createdAt).toLocaleString() : '未知'}<br>
                        ${request.lastExecutedAt ? `最后执行: ${new Date(request.lastExecutedAt).toLocaleString()}` : ''}
                        ${request.lastResponseStatus ? `| 最后状态: ${request.lastResponseStatus}` : ''}
                        ${request.lastResponseTime ? `| 响应时间: ${request.lastResponseTime}ms` : ''}
                    </div>
                </div>
            `;
        }

        // 加载请求到当前表单
        async function loadRequestToCurrentForm(requestId) {
            try {
                const request = await callAPI('/apiPostman/requests/get', { id: requestId });
                if (request) {
                    loadRequestToForm(request);
                    showNotification('请求已加载到表单', 'success');
                }
            } catch (error) {
                console.error('Failed to load request:', error);
                showNotification('加载请求失败', 'error');
            }
        }

        // 加载请求到表单
        function loadRequestToForm(request) {
            if (!request) request = currentViewingRequest;
            if (!request) return;

            document.getElementById('method').value = request.method || 'GET';
            document.getElementById('url').value = request.originalUrl || request.url || '';
            document.getElementById('headers').value = JSON.stringify(request.headers || {}, null, 2);
            document.getElementById('body').value = request.body || '';
            document.getElementById('preScript').value = request.preRequestScript || '';
            document.getElementById('testScript').value = request.testScript || '';

            // 加载参数信息
            pathParams = [];
            queryParams = [];

            if (request.pathParams) {
                Object.keys(request.pathParams).forEach(key => {
                    pathParams.push({
                        id: 'path_' + Date.now() + '_' + Math.random(),
                        name: key,
                        value: request.pathParams[key]
                    });
                });
            }

            if (request.queryParams) {
                Object.keys(request.queryParams).forEach(key => {
                    queryParams.push({
                        id: 'query_' + Date.now() + '_' + Math.random(),
                        name: key,
                        value: request.queryParams[key]
                    });
                });
            }

            renderPathParams();
            renderQueryParams();

            // 加载操作列表
            selectedPreRequestActions = request.preRequestActions || [];
            selectedPostActions = request.postActions || [];
            updateSelectedActionsDisplay();

            // 切换到HTTP请求标签页
            showTab('request');

            closeRequestDetailDialog();
        }

        // 关闭请求详情对话框
        function closeRequestDetailDialog() {
            document.getElementById('requestDetailDialog').style.display = 'none';
            currentViewingRequest = null;
        }

        // 删除历史请求
        async function deleteHttpRequest(requestId) {
            if (!confirm('确定要删除这个历史请求吗？')) {
                return;
            }

            try {
                await callAPI('/apiPostman/requests/delete', { id: requestId });
                showNotification('请求删除成功', 'success');
                loadHttpRequests();
            } catch (error) {
                console.error('Failed to delete request:', error);
                showNotification('删除请求失败', 'error');
            }
        }

        // 复制历史请求
        async function duplicateHttpRequest(requestId) {
            const newName = prompt('请输入新请求的名称:');
            if (!newName || !newName.trim()) {
                return;
            }

            try {
                await callAPI('/apiPostman/requests/duplicate', {
                    id: requestId,
                    newName: newName.trim()
                });
                showNotification('请求复制成功', 'success');
                loadHttpRequests();
            } catch (error) {
                console.error('Failed to duplicate request:', error);
                showNotification('复制请求失败', 'error');
            }
        }

        // 显示历史请求统计信息
        async function showRequestStats() {
            try {
                const stats = await callAPI('/apiPostman/requests/stats', {});
                const statsText = `
历史请求统计信息:
- 历史记录总数: ${stats.totalCount}
- 有效记录数: ${stats.activeCount}

分类统计:
${Object.entries(stats.categoryStats || {}).map(([category, count]) => `- ${category}: ${count}个`).join('\n')}

方法统计:
${Object.entries(stats.methodStats || {}).map(([method, count]) => `- ${method}: ${count}个`).join('\n')}
                `;
                alert(statsText);
            } catch (error) {
                console.error('Failed to get stats:', error);
                showNotification('获取统计信息失败', 'error');
            }
        }
    </script>
</body>
</html>
